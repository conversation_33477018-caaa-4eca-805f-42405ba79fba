// <PERSON>ript to apply complete database setup to Supabase
require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_KEY');
  console.error('\nPlease check your .env.local file.');
  process.exit(1);
}

async function applySetup() {
  try {
    console.log('🚀 Starting complete database setup...\n');

    // Read the setup SQL file
    const setupPath = path.join(__dirname, 'complete-database-setup.sql');
    
    if (!fs.existsSync(setupPath)) {
      throw new Error(`Setup file not found: ${setupPath}`);
    }

    const setupSQL = fs.readFileSync(setupPath, 'utf8');
    
    console.log('📄 Setup file loaded successfully');
    console.log(`📍 File: ${setupPath}`);
    console.log(`📏 Size: ${setupSQL.length} characters\n`);

    // Execute the SQL using Supabase REST API
    console.log('⏳ Executing database setup...');
    
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({ sql: setupSQL })
    });

    if (!response.ok) {
      // If the RPC doesn't exist, try executing via direct SQL
      console.log('⚠️  RPC method not available, trying alternative approach...');
      
      // Split SQL into statements and execute them one by one
      const statements = setupSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      console.log(`🔧 Found ${statements.length} SQL statements to execute\n`);

      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        
        if (!statement || statement.startsWith('--') || statement.trim() === '') {
          continue;
        }

        try {
          console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
          
          const stmtResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${supabaseServiceKey}`,
              'apikey': supabaseServiceKey
            },
            body: JSON.stringify({ sql: statement + ';' })
          });

          if (!stmtResponse.ok) {
            const errorText = await stmtResponse.text();
            throw new Error(`HTTP ${stmtResponse.status}: ${errorText}`);
          }

          successCount++;
          console.log(`✅ Statement ${i + 1} executed successfully`);
          
        } catch (error) {
          errorCount++;
          console.error(`❌ Error executing statement ${i + 1}:`);
          console.error(`   SQL: ${statement.substring(0, 100)}...`);
          console.error(`   Error: ${error.message}\n`);
          
          // Continue with other statements unless it's a critical error
          if (error.message.includes('already exists') || 
              error.message.includes('does not exist') ||
              error.message.includes('duplicate')) {
            console.log('   ℹ️  This error is likely safe to ignore (resource already exists)\n');
          }
        }
      }

      console.log('\n📊 Setup Summary:');
      console.log(`   ✅ Successful statements: ${successCount}`);
      console.log(`   ❌ Failed statements: ${errorCount}`);
      console.log(`   📝 Total statements: ${statements.length}`);

    } else {
      console.log('✅ Database setup executed successfully via RPC');
    }

    console.log('\n🎉 Complete database setup completed!');
    console.log('\n📋 What was created:');
    console.log('   • Users table with management fields');
    console.log('   • Projects table');
    console.log('   • Tasks table for kanban board');
    console.log('   • User activity logs table');
    console.log('   • User sessions table');
    console.log('   • Indexes for better performance');
    console.log('   • Row Level Security policies');
    console.log('   • Helper functions for user management');
    console.log('   • User statistics view');
    console.log('\n🔗 You can now access the application at:');
    console.log('   http://localhost:3000/studio');
    console.log('   http://localhost:3000/studio/users (User Management)');

  } catch (error) {
    console.error('\n💥 Setup failed with error:');
    console.error(error.message);
    console.error('\n🔍 Troubleshooting tips:');
    console.error('   1. Check your Supabase credentials in .env.local');
    console.error('   2. Ensure your Supabase project is accessible');
    console.error('   3. Verify you have the correct service role key');
    console.error('   4. Try running the SQL manually in Supabase SQL Editor');
    process.exit(1);
  }
}

// Test database connection first
async function testConnection() {
  try {
    console.log('🔍 Testing database connection...');
    
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      }
    });

    if (!response.ok) {
      throw new Error(`Database connection failed: HTTP ${response.status}`);
    }

    console.log('✅ Database connection successful\n');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error(`   ${error.message}\n`);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🏗️  Complete Database Setup Tool');
  console.log('===================================\n');

  // Test connection first
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.error('❌ Cannot proceed without database connection');
    process.exit(1);
  }

  // Apply setup
  await applySetup();
}

// Run the setup
main().catch(error => {
  console.error('\n💥 Unexpected error:', error);
  process.exit(1);
});
