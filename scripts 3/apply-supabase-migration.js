// <PERSON><PERSON><PERSON> to apply Supabase migrations using the Supabase CLI
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Create a temporary SQL file
const tempDir = os.tmpdir();
const tempFile = path.join(tempDir, 'migration.sql');

// Migration SQL
const migrationSQL = `
-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  project_name TEXT NOT NULL,
  project_description TEXT NOT NULL,
  market_feasibility JSONB NOT NULL,
  suggested_improvements JSONB NOT NULL,
  core_features JSONB NOT NULL,
  technical_requirements JSONB NOT NULL,
  pricing_model JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  progress INTEGER DEFAULT 0
);

-- Create index on user_id for faster queries
CREATE INDEX IF NOT EXISTS projects_user_id_idx ON public.projects (user_id);

-- Add RLS policies
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select only their own projects
CREATE POLICY select_own_projects 
  ON public.projects 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Policy to allow users to insert their own projects
CREATE POLICY insert_own_projects 
  ON public.projects 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Policy to allow users to update only their own projects
CREATE POLICY update_own_projects 
  ON public.projects 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Policy to allow users to delete only their own projects
CREATE POLICY delete_own_projects 
  ON public.projects 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at on update
CREATE TRIGGER update_projects_updated_at
BEFORE UPDATE ON public.projects
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
`;

// Write the migration SQL to the temp file
fs.writeFileSync(tempFile, migrationSQL);

console.log('Applying Supabase migration...');

try {
  // Check if Supabase CLI is installed
  try {
    execSync('supabase --version', { stdio: 'ignore' });
  } catch (error) {
    console.error('Supabase CLI is not installed. Please install it first:');
    console.error('npm install -g supabase');
    process.exit(1);
  }

  // Execute the SQL using the Supabase CLI
  // You need to be logged in to Supabase CLI and have the project linked
  execSync(`supabase db execute --file ${tempFile}`, { stdio: 'inherit' });

  console.log('Migration applied successfully!');
} catch (error) {
  console.error('Error applying migration:', error.message);
  process.exit(1);
} finally {
  // Clean up the temp file
  fs.unlinkSync(tempFile);
} 