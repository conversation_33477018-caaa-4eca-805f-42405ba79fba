#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to apply the idea validations table migration to Supabase
 * Run with: node scripts/apply-idea-validations-migration.js
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Read the migration SQL file
const migrationPath = path.join(__dirname, 'add-idea-validations-table.sql')
const migrationSQL = fs.readFileSync(migrationPath, 'utf8')

async function applyMigration() {
  console.log('🚀 Applying idea validations table migration...')
  
  try {
    // Execute the migration SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    })
    
    if (error) {
      // If the RPC doesn't exist, try direct query execution
      console.log('📝 Trying direct SQL execution...')
      
      // Split the SQL into individual statements
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0)
      
      for (const statement of statements) {
        console.log(`Executing: ${statement.substring(0, 50)}...`)
        const { error: stmtError } = await supabase.rpc('exec_sql', {
          sql: statement
        })
        
        if (stmtError) {
          console.error(`❌ Error executing statement: ${stmtError.message}`)
          throw stmtError
        }
      }
    }
    
    console.log('✅ Migration applied successfully!')
    console.log('📋 Created:')
    console.log('   - idea_validations table')
    console.log('   - Indexes for performance')
    console.log('   - Row Level Security policies')
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    console.error('\n📝 Please run the SQL manually in your Supabase dashboard:')
    console.error('   1. Go to your Supabase project dashboard')
    console.error('   2. Navigate to SQL Editor')
    console.error('   3. Copy and paste the contents of scripts/add-idea-validations-table.sql')
    console.error('   4. Run the SQL')
    process.exit(1)
  }
}

// Run the migration
applyMigration()
  .then(() => {
    console.log('\n🎉 Idea validations table is ready!')
    console.log('💡 Users can now save and cache their AI idea validation results.')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Unexpected error:', error)
    process.exit(1)
  })
