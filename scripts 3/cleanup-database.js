const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function cleanupDatabase() {
  try {
    console.log('🧹 Starting database cleanup...');
    
    // Delete all user flows
    console.log('Deleting all user flows...');
    const { error: userFlowsError } = await supabase
      .from('user_flows')
      .delete()
      .neq('id', 0); // Delete all rows
    
    if (userFlowsError) {
      console.error('Error deleting user flows:', userFlowsError);
    } else {
      console.log('✅ All user flows deleted successfully');
    }
    
    // Delete all projects
    console.log('Deleting all projects...');
    const { error: projectsError } = await supabase
      .from('projects')
      .delete()
      .neq('id', 0); // Delete all rows
    
    if (projectsError) {
      console.error('Error deleting projects:', projectsError);
    } else {
      console.log('✅ All projects deleted successfully');
    }
    
    console.log('🎉 Database cleanup completed!');
    console.log('The AI insights page should now be empty and ready for fresh data.');
    
  } catch (error) {
    console.error('Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup
cleanupDatabase();