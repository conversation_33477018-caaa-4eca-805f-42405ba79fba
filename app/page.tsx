'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform, useSpring, useMotionValue } from 'framer-motion';
import Hero from "@/components/hero"
import Features from "@/components/features"
import CallToAction from "@/components/call-to-action"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import Statistics from "@/components/statistics"
import ScrollReveal from '@/components/animations/ScrollReveal';
import ParallaxSection from '@/components/animations/ParallaxSection';
import FloatingIcons from '@/components/animations/FloatingIcons';
import BackgroundGrid from '@/components/animations/BackgroundGrid';
import TechyNetworkBG from "@/components/animations/TechyNetworkBG"
import dynamic from 'next/dynamic';
import ScrollTriggeredVideo from "@/components/ScrollTriggeredVideo"
import LiveUserFlowModel from "@/components/LiveUserFlowModel"
import SaaSJourneyFlow from "@/components/SaaSJourneyFlow"
import SaaSJourneyDashboard from "@/components/SaaSJourneyDashboard"
import HowItWorksSection from "@/components/HowItWorksSection"
import ModernUserFlow from "@/components/ModernUserFlow"
import TechUserJourney from "@/components/TechUserJourney"
import LiveTechFlowDiagram from "@/components/LiveTechFlowDiagram"
import MarketFeasibilityCard from "@/components/MarketFeasibilityCard"
import FeatureDemonstration from "@/components/FeatureDemonstration"
import { Button } from "@/components/ui/button"
import DashboardFeatureSection from "@/components/DashboardFeatureSection"
import AIInsightsFeatureSection from "@/components/AIInsightsFeatureSection"
import IdeaValidatorFeatureSection from "@/components/IdeaValidatorFeatureSection"
import EnhancedParallax from '@/components/animations/EnhancedParallax';
import FloatingElement from '@/components/animations/FloatingElement';
import SectionTransition from '@/components/animations/SectionTransition';
import AnimatedFeatures from '@/components/features/AnimatedFeatures';
import AnimatedFeaturesSection from '@/components/AnimatedFeaturesSection';
import PricingCard from "@/components/PricingCard"
import FeaturesSection from '@/app/sections/FeaturesSection';
import ParallaxFeaturesSection from '@/app/sections/ParallaxFeaturesSection';
import MagneticHover from '@/components/animations/MagneticHover';

const SplashScreen = dynamic(() => import('@/components/SplashScreen'), {
  ssr: false
});

const AnimatedLogo = dynamic(() => import('@/components/AnimatedLogo'), { ssr: false });

// Prefers-reduced-motion hook
const useReducedMotion = () => {
  const [prefersReduced, setPrefersReduced] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReduced(mediaQuery.matches);
    
    const onChange = () => setPrefersReduced(mediaQuery.matches);
    mediaQuery.addEventListener('change', onChange);
    return () => mediaQuery.removeEventListener('change', onChange);
  }, []);
  
  return prefersReduced;
};

export default function LandingPage() {
  const [showSplash, setShowSplash] = useState(true);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const prefersReducedMotion = useReducedMotion();
  const { scrollYProgress } = useScroll();
  
  // Mouse movement effect
  useEffect(() => {
    if (prefersReducedMotion) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight,
      });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [prefersReducedMotion]);

  // Smooth animations
  const springConfig = { stiffness: 100, damping: 30, restDelta: 0.001 };
  const smoothProgress = useSpring(scrollYProgress, springConfig);

  return (
    <>
      <AnimatePresence mode="wait">
        {showSplash ? (
          <SplashScreen onComplete={() => setShowSplash(false)} />
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
            className="relative min-h-screen"
          >
            {/* Background Effects */}
            <BackgroundGrid />
            {!prefersReducedMotion && <FloatingIcons />}

            {/* Main content */}
            <main className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
              {/* Navigation */}
              <Navbar />

              <div className="space-y-16 sm:space-y-24 lg:space-y-32">
                {/* Hero Section */}
                <div className="relative py-20 md:py-32">
                  <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                    <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                      {/* Left column - Hero content */}
                      <div className="lg:col-span-7 space-y-8">
                        <h1 className="text-5xl sm:text-6xl font-bold text-white">
                          SassifyX
                          <br />
                          <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">
                            ideas into
                          </span>
                          <br />
                          <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-blue-500">
                            reality
                          </span>
                        </h1>
                        
                        <h2 className="text-2xl font-medium text-white">
                          Sketch it. <span className="text-blue-400">SaaSify</span> it. <span className="text-purple-400">Xecute</span> it.
                        </h2>
                        
                        <p className="text-xl text-slate-300">
                          AI-powered platform that analyzes, validates, and helps you build successful SaaS applications from concept to launch.
                        </p>
                        
                        <div className="flex flex-wrap gap-4">
                          <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0">
                            Get Started Free
                          </Button>
                          <Button size="lg" variant="outline" className="text-white border-white/20 hover:bg-white/10">
                            Explore Features
                          </Button>
                        </div>
                      </div>
                      
                      {/* Right column - Market Feasibility */}
                      <div className="lg:col-span-5">
                        <MarketFeasibilityCard />
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Feature Demonstration */}
                <div className="relative py-16 md:py-24">
                  <div className="absolute inset-0 bg-gradient-to-b from-slate-950/50 via-blue-950/10 to-purple-950/10 z-0"></div>
                  <div className="relative z-10">
                    <FeatureDemonstration />
                  </div>
                </div>

                {/* Parallax Features Section */}
                <ParallaxFeaturesSection />

                {/* How It Works Section */}
                <div className="relative py-16 md:py-24" id="how-it-works">
                  <div className="absolute inset-0 bg-gradient-to-b from-slate-950/50 via-blue-950/10 to-purple-950/10 z-0"></div>
                  
                  {/* Live Tech Flow Diagram */}
                  <div className="relative z-10">
                    <LiveTechFlowDiagram />
                  </div>
                </div>
                
                {/* Statistics with glow effect */}
                <div className="relative py-16 md:py-24">
                  <ScrollReveal>
                    <motion.div
                      whileHover={{ 
                        scale: 1.02,
                        transition: { duration: 0.3 }
                      }}
                    >
                      <Statistics />
                    </motion.div>
                  </ScrollReveal>
                </div>

                {/* Features Grid */}
                <ParallaxSection speed={0.2}>
                  <div className="relative" id="features">
                    {/* Animated Features Section */}
                    <AnimatedFeaturesSection />
                  </div>
                </ParallaxSection>
                
                {/* Pricing Section */}
                <section id="pricing" className="relative py-24 overflow-hidden">
                  {/* Background elements */}
                  <div className="absolute inset-0 bg-gradient-to-b from-slate-950 via-blue-950/20 to-purple-950/20 z-0"></div>
                  <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
                    <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/5 rounded-full filter blur-3xl"></div>
                    <div className="absolute bottom-20 right-20 w-64 h-64 bg-purple-500/5 rounded-full filter blur-3xl"></div>
                    <div className="absolute top-1/2 left-1/3 w-96 h-96 bg-cyan-500/5 rounded-full filter blur-3xl"></div>
                  </div>
                  
                  {/* Circuit board pattern overlay */}
                  <div className="absolute inset-0 bg-[url('/circuit-pattern.png')] bg-repeat opacity-5 z-0"></div>
                  
                  <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.7 }}
                        viewport={{ once: true }}
                      >
                        <h2 className="text-4xl md:text-5xl font-bold mb-4 gradient-text">Pricing</h2>
                        <div className="relative h-1 w-24 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-6 rounded-full">
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"></div>
                        </div>
                        <p className="mt-6 text-lg text-slate-300 max-w-2xl mx-auto">
                          Choose the plan that best fits your journey from idea to successful SaaS
                        </p>
                      </motion.div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
                      <PricingCard
                        name="Starter"
                        price={0}
                        features={[
                          "Basic AI Analysis",
                          "Community Support",
                          "Limited Projects"
                        ]}
                      />
                      <PricingCard
                        name="Pro"
                        price={29}
                        features={[
                          "All Starter Features",
                          "Unlimited Projects",
                          "Premium AI Tools",
                          "Email Support"
                        ]}
                        isRecommended={true}
                      />
                      <PricingCard
                        name="Enterprise"
                        price={0}
                        features={[
                          "All Pro Features",
                          "Custom Integrations",
                          "Dedicated Support",
                          "SLA Guarantees"
                        ]}
                      />
                    </div>
                    
                    <div className="text-center mt-8 text-sm text-slate-400">
                      <p>All plans include a 14-day free trial. No credit card required.</p>
                      <p className="mt-2">
                        Need a custom solution? <a href="#" className="text-blue-400 hover:text-blue-300">Contact us</a> for tailored pricing.
                      </p>
                    </div>
                  </div>
                </section>

                {/* About Section */}
                <section id="about" className="relative py-24 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-b from-slate-950/50 via-blue-950/20 to-purple-950/20 z-0"></div>
                  
                  <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
                    <motion.h2 
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.7 }}
                      viewport={{ once: true }}
                      className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
                    >
                      About SassifyX
                    </motion.h2>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.7 }}
                      viewport={{ once: true }}
                      className="max-w-4xl mx-auto"
                    >
                      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-xl">
                        <div className="p-8 relative z-10">
                          {/* Card Header */}
                          <div className="flex items-center justify-between mb-8">
                            <div className="flex items-center space-x-4">
                              <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                              </div>
                              <h3 className="text-2xl font-bold text-white">Our Vision</h3>
                            </div>
                            <div className="text-blue-400">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                              </svg>
                            </div>
                          </div>

                          {/* Main Content */}
                          <div className="space-y-8">
                            <div>
                              <h4 className="text-xl font-semibold text-white mb-3">Mission & Technology</h4>
                              <p className="text-slate-200 leading-relaxed text-lg">
                                SassifyX is an AI-powered platform designed to help entrepreneurs and developers validate, build, and 
                                launch successful SaaS products. Built by passionate engineers and designers, we leverage cutting-edge 
                                AI and cloud technology to empower your SaaS journey.
                              </p>
                            </div>

                            {/* Values Grid */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                              <div className="p-4 rounded-xl bg-white/5">
                                <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mb-3">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                  </svg>
                                </div>
                                <h5 className="text-lg font-semibold text-white mb-2">Innovation</h5>
                                <p className="text-slate-300">Pushing boundaries with cutting-edge solutions</p>
                              </div>

                              <div className="p-4 rounded-xl bg-white/5">
                                <div className="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mb-3">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                  </svg>
                                </div>
                                <h5 className="text-lg font-semibold text-white mb-2">Security</h5>
                                <p className="text-slate-300">Protecting your data at every step</p>
                              </div>

                              <div className="p-4 rounded-xl bg-white/5">
                                <div className="w-12 h-12 rounded-full bg-emerald-500/20 flex items-center justify-center mb-3">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                  </svg>
                                </div>
                                <h5 className="text-lg font-semibold text-white mb-2">Community</h5>
                                <p className="text-slate-300">Building together with shared knowledge</p>
                              </div>
                            </div>

                            {/* Footer */}
                            <div className="mt-8 pt-8 border-t border-white/10 flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <span className="inline-block w-2 h-2 bg-green-500 rounded-full"></span>
                                <span className="text-slate-300">Always evolving, always improving</span>
                              </div>
                              <button className="px-6 py-2 rounded-lg bg-blue-500 text-white font-medium hover:bg-blue-600 transition-colors">
                                Learn More
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </section>

                {/* CTA Section with ripple effect */}
                <ParallaxSection speed={0.2}>
                  <ScrollReveal>
                    <motion.div
                      whileInView={{
                        scale: [0.95, 1],
                        opacity: [0, 1],
                      }}
                      transition={{ duration: 0.5 }}
                      className="relative overflow-hidden"
                    >
                      <CallToAction />
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"
                        initial={{ scale: 0, opacity: 0 }}
                        whileInView={{
                          scale: [1, 1.5],
                          opacity: [0.5, 0],
                        }}
                        transition={{
                          duration: 1,
                          ease: "easeOut",
                        }}
                      />
                    </motion.div>
                  </ScrollReveal>
                </ParallaxSection>

                <ScrollReveal>
                  <Footer />
                </ScrollReveal>
              </div>
            </main>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
