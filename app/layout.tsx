import type React from "react"
import "@/styles/globals.css"
import "@/styles/animations.css"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/components/providers/auth-provider"
import { Toaster } from "sonner"

export const metadata = {
  title: "SaaS Ideas",
  description: "A platform for SaaS ideas and projects",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Generate a timestamp for cache busting
  const timestamp = new Date().getTime();
  
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="cache-control" content="no-cache, no-store, must-revalidate" />
        <meta name="pragma" content="no-cache" />
        <meta name="expires" content="0" />
        <meta name="version" content={`${timestamp}`} />
      </head>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            {children}
            <Toaster
              theme="dark"
              position="top-right"
              toastOptions={{
                style: {
                  background: 'rgb(30 41 59)',
                  border: '1px solid rgb(51 65 85)',
                  color: 'rgb(248 250 252)',
                },
              }}
            />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
