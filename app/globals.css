@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Premium color palette */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;
    --color-primary-950: #172554;

    /* Dark theme optimized colors */
    --color-neutral-50: #f8fafc;
    --color-neutral-100: #f1f5f9;
    --color-neutral-200: #e2e8f0;
    --color-neutral-300: #cbd5e1;
    --color-neutral-400: #94a3b8;
    --color-neutral-500: #64748b;
    --color-neutral-600: #475569;
    --color-neutral-700: #334155;
    --color-neutral-800: #1e293b;
    --color-neutral-900: #0f172a;
    --color-neutral-950: #020617;

    /* Accent colors for premium feel */
    --color-accent-blue: #3b82f6;
    --color-accent-purple: #8b5cf6;
    --color-accent-emerald: #10b981;
    --color-accent-amber: #f59e0b;

    --background: 0 0% 4%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    --radius: 0.75rem;

    /* Premium typography */
    --font-sans: "Inter", system-ui, sans-serif;
    --font-mono: "Fira Code", monospace;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: #0a0a0a;
    background-attachment: fixed;
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Performance optimizations */
  * {
    box-sizing: border-box;
  }

  /* Remove smooth scrolling for instant jumps */
  html {
    scroll-behavior: auto;
  }

  /* Optimize font rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(15, 23, 42, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.5);
  }

  /* Premium glow effects */
  .glow-blue {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.4),
      0 0 40px rgba(59, 130, 246, 0.2),
      0 0 80px rgba(59, 130, 246, 0.1);
  }

  .glow-purple {
    box-shadow:
      0 0 20px rgba(139, 92, 246, 0.4),
      0 0 40px rgba(139, 92, 246, 0.2),
      0 0 80px rgba(139, 92, 246, 0.1);
  }

  .glow-emerald {
    box-shadow:
      0 0 20px rgba(16, 185, 129, 0.4),
      0 0 40px rgba(16, 185, 129, 0.2),
      0 0 80px rgba(16, 185, 129, 0.1);
  }

  .glow-amber {
    box-shadow:
      0 0 20px rgba(245, 158, 11, 0.4),
      0 0 40px rgba(245, 158, 11, 0.2),
      0 0 80px rgba(245, 158, 11, 0.1);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(24px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-card {
    background: rgba(15, 23, 42, 0.4);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Premium gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced shadows */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* 3D Card Effects */
  .card-3d {
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  .card-3d-inner {
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .card-3d:hover .card-3d-inner {
    transform: rotateX(5deg) rotateY(10deg) translateZ(20px);
  }

  /* Premium button effects */
  .btn-premium {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
    box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(139, 92, 246, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-premium:hover {
    transform: translateY(-2px);
    box-shadow:
      0 8px 25px rgba(59, 130, 246, 0.5),
      0 0 30px rgba(139, 92, 246, 0.4);
  }

  .btn-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-premium:hover::before {
    left: 100%;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Premium card hover effects */
  .card-premium {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    background: rgba(15, 23, 42, 0.6);
    backdrop-filter: blur(20px);
  }

  .card-premium:hover {
    transform: translateY(-4px) scale(1.02);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* Floating animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Pulse glow animation */
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  @keyframes pulse-glow {
    from {
      box-shadow: 0 0 10px currentColor;
    }
    to {
      box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    }
  }

  /* Subtle texture overlay */
  .texture-overlay {
    background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }

  /* Performance optimizations for animations */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Optimize rendering for large screens */
  @media (min-width: 1920px) {
    .container {
      max-width: 1400px;
    }
  }

  /* Ensure proper rendering on high DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .gradient-text {
      background-size: 200% 200%;
    }
  }

  /* Smooth task transitions */
  .task-transition {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
  }

  .task-transition:hover {
    transform: translateY(-2px);
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .card-3d:hover .card-3d-inner {
      transform: none;
    }

    .task-transition {
      transition: none !important;
      animation: none !important;
    }

    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* Statistics Section Animations */
.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 8s linear infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.stat-card {
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  transform: translateZ(20px);
}

/* Glass Morphism Effects */
.glass-morphism {
  background: rgba(15, 23, 42, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Glow Effects */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(
    45deg,
    rgba(59, 130, 246, 0.5),
    rgba(147, 51, 234, 0.5),
    rgba(59, 130, 246, 0.5)
  );
  filter: blur(15px);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  z-index: -1;
}

.glow-effect:hover::before {
  opacity: 1;
}

/* Asymmetrical Layout Styles */
@media (min-width: 1024px) {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    transform: perspective(1000px) rotateX(5deg);
  }

  .stats-grid > div:nth-child(odd) {
    transform: translateY(-2rem);
  }
}

/* Partner Logo Animation */
.partner-logo {
  filter: grayscale(100%);
  opacity: 0.6;
  transition: all 0.3s ease;
}

.partner-logo:hover {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(
    to bottom,
    rgba(59, 130, 246, 0.5),
    rgba(147, 51, 234, 0.5)
  );
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    to bottom,
    rgba(59, 130, 246, 0.7),
    rgba(147, 51, 234, 0.7)
  );
}
