import ScrollBasedReveal from '@/components/ScrollBasedReveal';

const featureSections = [
  {
    id: 'comprehensive-dashboard',
    title: 'Comprehensive Dashboard',
    description: 'Track your SaaS projects, completion rates, and time saved with our intuitive dashboard that provides real-time insights and analytics.',
    image: '/images/features/dashboard.png'
  },
  {
    id: 'ai-powered-insights',
    title: 'AI-Powered Insights',
    description: 'Leverage advanced AI analytics to gain valuable insights about your SaaS projects and make data-driven decisions for optimal results.',
    image: '/images/features/ai-insights.png'
  },
  {
    id: 'idea-validation',
    title: 'Idea Validation',
    description: 'Validate your SaaS idea with comprehensive market analysis, technical feasibility, and business viability checks before investing significant resources.',
    image: '/images/features/idea-validation.png'
  }
];

export default function FeaturesSection() {
  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">Powerful Features</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Discover how our platform can transform your SaaS development journey
          </p>
        </div>
        
        <ScrollBasedReveal sections={featureSections} />
      </div>
    </section>
  );
} 