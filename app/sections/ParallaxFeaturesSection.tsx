'use client';

import { motion, useScroll, useTransform, useSpring, useMotionTemplate } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';
import { IconChartBar, IconBrain, IconBulb } from '@tabler/icons-react';

const features = [
  {
    title: "Comprehensive Dashboard",
    description: "Track your SaaS projects, completion rates, and time saved with our intuitive dashboard that provides real-time insights and analytics.",
    extendedInfo: [
      { stat: "40%", label: "Faster Project Completion" },
      { stat: "24/7", label: "Real-time Monitoring" },
      { stat: "500+", label: "Active Users" }
    ],
    image: "/images/demo/dashboard.png",
    gradient: "from-blue-500/20 via-blue-400/10 to-transparent",
    accentColor: "text-blue-400",
    icon: IconChartBar,
    badge: "Most Popular"
  },
  {
    title: "AI-Powered Insights",
    description: "Leverage advanced AI analytics to gain valuable insights about your SaaS projects and make data-driven decisions for optimal results.",
    extendedInfo: [
      { stat: "95%", label: "Prediction Accuracy" },
      { stat: "10x", label: "Faster Analysis" },
      { stat: "1M+", label: "Data Points Analyzed" }
    ],
    image: "/images/demo/ai-insights.png",
    gradient: "from-purple-500/20 via-purple-400/10 to-transparent",
    accentColor: "text-purple-400",
    icon: IconBrain,
    badge: "AI Powered"
  },
  {
    title: "Idea Validation",
    description: "Validate your SaaS idea with comprehensive market analysis, technical feasibility, and business viability checks before investing significant resources.",
    extendedInfo: [
      { stat: "85%", label: "Success Rate" },
      { stat: "48h", label: "Validation Time" },
      { stat: "50+", label: "Market Metrics" }
    ],
    image: "/images/demo/idea-validator.png",
    gradient: "from-emerald-500/20 via-emerald-400/10 to-transparent",
    accentColor: "text-emerald-400",
    icon: IconBulb,
    badge: "New"
  }
];

const AnimatedBackground = () => {
  return (
    <>
      {/* Animated gradient orbs */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-[500px] h-[500px] rounded-full blur-[100px] opacity-20`}
            animate={{
              x: [
                `${Math.sin(i * 4) * 50}%`,
                `${Math.sin((i + 1) * 4) * 50}%`
              ],
              y: [
                `${Math.cos(i * 4) * 50}%`,
                `${Math.cos((i + 1) * 4) * 50}%`
              ],
              background: [
                'rgb(59, 130, 246)',
                'rgb(147, 51, 234)',
                'rgb(16, 185, 129)',
                'rgb(59, 130, 246)'
              ]
            }}
            transition={{
              duration: 15 + i * 3,
              repeat: Infinity,
              ease: "linear"
            }}
            style={{
              left: `${33 * (i + 1)}%`,
              top: `${33 * (i + 1)}%`,
            }}
          />
        ))}
      </div>

      {/* Animated grid with parallax effect */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,.07)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,.07)_1px,transparent_1px)] bg-[size:100px_100px]"
          animate={{
            backgroundPosition: ['0px 0px', '100px 100px']
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      {/* Vignette effect */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,transparent_20%,rgba(0,0,0,0.5)_150%)]" />
    </>
  );
};

const FloatingParticles = ({ color }: { color: string }) => {
  return (
    <div className="absolute inset-0 pointer-events-none">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className={`absolute w-1 h-1 rounded-full ${color}`}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1.5, 0],
            y: [-20, -60, -20],
            x: Math.sin(i) * 30
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: i * 0.3,
            ease: "easeInOut"
          }}
          style={{
            left: `${15 + i * 10}%`,
            top: '50%'
          }}
        />
      ))}
    </div>
  );
};

const ProgressIndicator = ({ progress }: { progress: number }) => {
  return (
    <div className="fixed right-8 top-1/2 -translate-y-1/2 w-1 h-40 bg-gray-800 rounded-full overflow-hidden">
      <motion.div
        className="w-full bg-gradient-to-b from-blue-400 via-purple-400 to-emerald-400"
        style={{ height: `${progress * 100}%`, originY: 0 }}
      />
    </div>
  );
};

export default function ParallaxFeaturesSection() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [activeFeature, setActiveFeature] = useState(0);
  const featureRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return;

      const container = containerRef.current;
      const containerTop = container.offsetTop;
      const containerHeight = container.offsetHeight;
      const viewportHeight = window.innerHeight;
      const scrollPosition = window.scrollY;

      const scrollProgress = (scrollPosition - containerTop + viewportHeight / 2) / containerHeight;
      const featureIndex = Math.floor(scrollProgress * features.length);
      
      setActiveFeature(Math.max(0, Math.min(featureIndex, features.length - 1)));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section 
      ref={containerRef}
      className="relative bg-gray-900"
      style={{ 
        height: `${features.length * 100}vh`,
        scrollSnapType: 'y mandatory'
      }}
    >
      <AnimatedBackground />

      {/* Fixed position content container */}
      <div className="sticky top-0 h-screen flex items-center justify-center overflow-hidden">
        <div className="container mx-auto px-4 pt-24">
          {/* Header - Moved outside the content area */}
          <div className="absolute top-8 left-0 right-0 text-center z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-5xl font-bold mb-4 bg-gradient-to-r from-blue-400 via-purple-400 to-emerald-400 text-transparent bg-clip-text">
                Powerful Features
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Discover how our platform can transform your SaaS development journey with cutting-edge tools and intelligent insights
              </p>
            </motion.div>
          </div>

          {/* Features */}
          <div className="relative max-w-7xl mx-auto">
            {features.map((feature, index) => {
              const isActive = index === activeFeature;
              const isNext = index === activeFeature + 1;
              const isPrev = index === activeFeature - 1;

              return (
                <div
                  key={feature.title}
                  ref={el => featureRefs.current[index] = el}
                  className="grid grid-cols-2 gap-16 items-center absolute inset-x-0"
                  style={{
                    opacity: isActive ? 1 : 0,
                    pointerEvents: isActive ? 'auto' : 'none',
                    transition: 'all 0.5s ease-in-out',
                    transform: `translateY(${isActive ? '0%' : isNext ? '100%' : isPrev ? '-100%' : '0%'})`,
                  }}
                >
                  {/* Text Content - Left Side */}
                  <motion.div
                    className="space-y-6"
                    initial={false}
                    animate={{
                      y: isActive ? 0 : isNext ? 100 : -100,
                      opacity: isActive ? 1 : 0,
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="space-y-4">
                      {feature.badge && (
                        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${feature.accentColor} border border-current`}>
                          {feature.badge}
                        </span>
                      )}
                      
                      <div className="flex items-center gap-3">
                        <feature.icon className={`w-8 h-8 ${feature.accentColor}`} />
                        <h3 className={`text-3xl font-bold ${feature.accentColor}`}>
                          {feature.title}
                        </h3>
                      </div>
                      
                      <p className="text-lg text-gray-300 leading-relaxed">
                        {feature.description}
                      </p>

                      <div className="grid grid-cols-3 gap-6 mt-8">
                        {feature.extendedInfo.map((info, i) => (
                          <motion.div
                            key={info.label}
                            className="text-center p-4 rounded-lg bg-gray-800/50 backdrop-blur-sm"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: isActive ? 1 : 0, y: isActive ? 0 : 20 }}
                            transition={{ delay: i * 0.1 }}
                          >
                            <div className={`text-2xl font-bold mb-2 ${feature.accentColor}`}>
                              {info.stat}
                            </div>
                            <div className="text-sm text-gray-400">
                              {info.label}
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </motion.div>

                  {/* Image - Right Side */}
                  <motion.div
                    className="relative"
                    initial={false}
                    animate={{
                      y: isActive ? 0 : isNext ? 100 : -100,
                      opacity: isActive ? 1 : 0,
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="relative rounded-2xl overflow-hidden bg-gray-800/50 backdrop-blur-sm p-6 shadow-2xl">
                      {/* Gradient overlay */}
                      <div className={`absolute inset-0 ${feature.gradient} opacity-20`} />
                      
                      {/* Card inner glow */}
                      <div className={`absolute inset-0 ${feature.gradient.replace('20', '10')} blur-2xl`} />
                      
                      {/* Content */}
                      <div className="relative z-10">
                        <div className="aspect-video rounded-lg overflow-hidden">
                          <img
                            src={feature.image}
                            alt={feature.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Progress indicator */}
      <div className="fixed right-8 top-1/2 -translate-y-1/2 flex flex-col gap-2">
        {features.map((_, index) => (
          <div
            key={index}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === activeFeature ? 'bg-white scale-150' : 'bg-gray-600'
            }`}
          />
        ))}
      </div>
    </section>
  );
} 