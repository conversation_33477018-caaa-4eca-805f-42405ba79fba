-- Create user_flows table for storing user flow diagrams
CREATE TABLE IF NOT EXISTS user_flows (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_name TEXT NOT NULL UNIQUE,
  flow_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on project_name for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_flows_project_name ON user_flows(project_name);

-- Create index on created_at for ordering
CREATE INDEX IF NOT EXISTS idx_user_flows_created_at ON user_flows(created_at);

-- Add RLS (Row Level Security) policies if needed
-- ALTER TABLE user_flows ENABLE ROW LEVEL SECURITY;

-- Example policy (uncomment if you want to add user-based access control)
-- CREATE POLICY "Users can view their own flows" ON user_flows
--   FOR SELECT USING (auth.uid()::text = user_id);

-- CREATE POLICY "Users can insert their own flows" ON user_flows
--   FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- CREATE POLICY "Users can update their own flows" ON user_flows
--   FOR UPDATE USING (auth.uid()::text = user_id);

-- CREATE POLICY "Users can delete their own flows" ON user_flows
--   FOR DELETE USING (auth.uid()::text = user_id);