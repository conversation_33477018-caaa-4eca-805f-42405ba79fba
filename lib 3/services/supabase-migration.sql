-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  project_name TEXT NOT NULL,
  project_description TEXT NOT NULL,
  market_feasibility JSONB NOT NULL,
  suggested_improvements JSONB NOT NULL,
  core_features JSONB NOT NULL,
  technical_requirements JSONB NOT NULL,
  pricing_model JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  progress INTEGER DEFAULT 0,
  -- AI-generated content storage
  generated_tasks JSONB,
  tasks_explanation TEXT,
  tasks_generated_at TIMESTAMPTZ,
  -- User flow data
  user_flow_data JSONB,
  user_flow_generated_at TIMESTAMPTZ,
  -- Memory bank data
  memory_bank_data JSONB,
  memory_bank_generated_at TIMESTAMPTZ,
  -- Overview data
  overview_data JSONB,
  overview_generated_at TIMESTAMPTZ
);

-- Create tasks table for kanban board
CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'done')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  category TEXT,
  estimated_hours INTEGER,
  dependencies JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create user_flows table
CREATE TABLE IF NOT EXISTS public.user_flows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  flow_data JSONB NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  version INTEGER DEFAULT 1
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS projects_user_id_idx ON public.projects (user_id);
CREATE INDEX IF NOT EXISTS projects_project_name_idx ON public.projects (project_name);
CREATE INDEX IF NOT EXISTS tasks_project_id_idx ON public.tasks (project_id);
CREATE INDEX IF NOT EXISTS tasks_status_idx ON public.tasks (status);
CREATE INDEX IF NOT EXISTS user_flows_project_id_idx ON public.user_flows (project_id);

-- Add RLS policies
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_flows ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select only their own projects
CREATE POLICY select_own_projects 
  ON public.projects 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Policy to allow users to insert their own projects
CREATE POLICY insert_own_projects 
  ON public.projects 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Policy to allow users to update only their own projects
CREATE POLICY update_own_projects 
  ON public.projects 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Policy to allow users to delete only their own projects
CREATE POLICY delete_own_projects
  ON public.projects
  FOR DELETE
  USING (auth.uid() = user_id);

-- Tasks table policies
CREATE POLICY select_own_tasks
  ON public.tasks
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = tasks.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY insert_own_tasks
  ON public.tasks
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = tasks.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY update_own_tasks
  ON public.tasks
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = tasks.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY delete_own_tasks
  ON public.tasks
  FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = tasks.project_id
    AND projects.user_id = auth.uid()
  ));

-- User flows table policies
CREATE POLICY select_own_user_flows
  ON public.user_flows
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = user_flows.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY insert_own_user_flows
  ON public.user_flows
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = user_flows.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY update_own_user_flows
  ON public.user_flows
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = user_flows.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY delete_own_user_flows
  ON public.user_flows
  FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = user_flows.project_id
    AND projects.user_id = auth.uid()
  ));

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at on update
CREATE TRIGGER update_projects_updated_at
BEFORE UPDATE ON public.projects
FOR EACH ROW
EXECUTE FUNCTION update_modified_column(); 