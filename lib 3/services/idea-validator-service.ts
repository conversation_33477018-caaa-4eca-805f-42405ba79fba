export interface IdeaValidationInput {
  ideaTitle: string
  ideaDescription: string
  targetMarket: string
  businessModel: string
  technicalComplexity?: 'low' | 'medium' | 'high'
  budget?: number
  timeline?: string
}

export interface IdeaValidationResult {
  overallScore: number // 0-100
  marketPotential: {
    score: number
    marketSize: string
    competitorCount: string
    demandLevel: 'low' | 'medium' | 'high'
    insights: string[]
  }
  technicalFeasibility: {
    score: number
    complexity: 'low' | 'medium' | 'high'
    requiredSkills: string[]
    estimatedDevelopmentTime: string
    technicalRisks: string[]
  }
  businessViability: {
    score: number
    revenueModel: string[]
    estimatedRevenue: string
    breakEvenTime: string
    fundingRequired: string
    businessRisks: string[]
  }
  competitiveAnalysis: {
    score: number
    mainCompetitors: string[]
    competitiveAdvantage: string[]
    marketGap: string
    differentiationStrategy: string[]
  }
  recommendations: {
    strengths: string[]
    weaknesses: string[]
    opportunities: string[]
    threats: string[]
    nextSteps: string[]
    pivotSuggestions?: string[]
  }
  successProbability: number // 0-100
  riskLevel: 'low' | 'medium' | 'high'
  validationDate: string
}



export function getValidationScoreColor(score: number): string {
  if (score >= 80) return "text-green-400"
  if (score >= 60) return "text-yellow-400"
  if (score >= 40) return "text-orange-400"
  return "text-red-400"
}

export function getValidationScoreBg(score: number): string {
  if (score >= 80) return "bg-green-500/20 border-green-500/30"
  if (score >= 60) return "bg-yellow-500/20 border-yellow-500/30"
  if (score >= 40) return "bg-orange-500/20 border-orange-500/30"
  return "bg-red-500/20 border-red-500/30"
}

export function getRiskLevelColor(riskLevel: string): string {
  switch (riskLevel) {
    case 'low': return "text-green-400"
    case 'medium': return "text-yellow-400"
    case 'high': return "text-red-400"
    default: return "text-slate-400"
  }
}
