"use server";

import { createClient } from '@/lib/supabase/server'
import { IdeaValidationInput, IdeaValidationResult } from '@/lib/services/idea-validator-service'
import { generateInputHash } from '@/lib/utils/validation-hash'

export interface IdeaValidationRecord {
  id: string
  user_id: string
  idea_title: string
  idea_description: string
  target_market: string
  business_model: string
  technical_complexity: string
  budget?: number
  timeline?: string
  input_hash: string
  validation_result: IdeaValidationResult
  created_at: string
  updated_at: string
}



// Check if validation exists for the same input (SERVER ACTION)
export async function findExistingValidationAction(
  input: IdeaValidationInput
): Promise<IdeaValidationRecord | null> {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      console.log('No authenticated user found')
      return null
    }
    
    const inputHash = generateInputHash(input)
    console.log('Searching for existing validation with hash:', inputHash)
    
    const { data, error } = await supabase
      .from('idea_validations')
      .select('*')
      .eq('user_id', user.id)
      .eq('input_hash', inputHash)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle()
    
    if (error) {
      console.error('Error finding existing validation:', error)
      return null
    }
    
    if (data) {
      console.log('Found existing validation:', data.id)
    } else {
      console.log('No existing validation found')
    }
    
    return data
  } catch (error) {
    console.error('Error in findExistingValidationAction:', error)
    return null
  }
}

// Save validation result to database (SERVER ACTION)
export async function saveValidationResultAction(
  input: IdeaValidationInput,
  result: IdeaValidationResult
): Promise<{ success: boolean; data?: IdeaValidationRecord; error?: string }> {
  try {
    console.log('🔄 Starting saveValidationResultAction...')
    const supabase = await createClient()

    // Get current user
    console.log('🔍 Getting current user...')
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError) {
      console.error('❌ Auth error:', authError)
      return { success: false, error: `Authentication error: ${authError.message}` }
    }

    if (!user) {
      console.error('❌ No user found')
      return { success: false, error: 'User not authenticated' }
    }

    console.log('✅ User authenticated:', user.id)

    const inputHash = generateInputHash(input)
    console.log('🔑 Generated hash:', inputHash)

    // Check if validation already exists
    const { data: existing } = await supabase
      .from('idea_validations')
      .select('id')
      .eq('user_id', user.id)
      .eq('input_hash', inputHash)
      .maybeSingle()

    if (existing) {
      console.log('⚠️ Validation already exists:', existing.id)
      // Return success with a special message indicating it already exists
      return { success: true, data: null, error: 'Validation with identical input already exists' }
    }

    const validationRecord = {
      user_id: user.id,
      idea_title: input.ideaTitle,
      idea_description: input.ideaDescription,
      target_market: input.targetMarket,
      business_model: input.businessModel,
      technical_complexity: input.technicalComplexity || 'medium',
      budget: input.budget,
      timeline: input.timeline,
      input_hash: inputHash,
      validation_result: result
    }

    console.log('💾 Inserting validation record...')
    const { data, error } = await supabase
      .from('idea_validations')
      .insert(validationRecord)
      .select()
      .single()

    if (error) {
      console.error('❌ Database error:', error)
      return { success: false, error: `Database error: ${error.message}` }
    }

    console.log('✅ Validation saved successfully:', data.id)
    return { success: true, data }

  } catch (error) {
    console.error('❌ Unexpected error in saveValidationResultAction:', error)
    return { success: false, error: `Unexpected error: ${error}` }
  }
}

// Get all validations for current user (SERVER ACTION)
export async function getUserValidationsAction(): Promise<{ success: boolean; data?: IdeaValidationRecord[]; error?: string }> {
  try {
    console.log('🔄 Starting getUserValidationsAction...')
    const supabase = await createClient()

    // Get current user
    console.log('🔍 Getting current user...')
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError) {
      console.error('❌ Auth error:', authError)
      return { success: false, error: `Authentication error: ${authError.message}` }
    }

    if (!user) {
      console.error('❌ No user found')
      return { success: false, error: 'User not authenticated' }
    }

    console.log('✅ User authenticated:', user.id)

    console.log('📊 Fetching validations...')
    const { data, error } = await supabase
      .from('idea_validations')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Database error:', error)
      return { success: false, error: `Database error: ${error.message}` }
    }

    console.log(`✅ Found ${data?.length || 0} validations`)
    return { success: true, data: data || [] }

  } catch (error) {
    console.error('❌ Unexpected error in getUserValidationsAction:', error)
    return { success: false, error: `Unexpected error: ${error}` }
  }
}

// Delete validation (SERVER ACTION)
export async function deleteValidationAction(id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return { success: false, error: 'User not authenticated' }
    }
    
    const { error } = await supabase
      .from('idea_validations')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)
    
    if (error) {
      console.error('Error deleting validation:', error)
      return { success: false, error: `Failed to delete validation: ${error.message}` }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in deleteValidationAction:', error)
    return { success: false, error: 'Unexpected error occurred' }
  }
}
