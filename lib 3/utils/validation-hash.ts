import { IdeaValidationInput } from '@/lib/services/idea-validator-service'
import crypto from 'crypto'

// Generate a hash from the input data for caching
export function generateInputHash(input: IdeaValidationInput): string {
  const normalizedInput = {
    ideaTitle: input.ideaTitle.trim().toLowerCase(),
    ideaDescription: input.ideaDescription.trim().toLowerCase(),
    targetMarket: input.targetMarket.trim().toLowerCase(),
    businessModel: input.businessModel.trim().toLowerCase(),
    technicalComplexity: input.technicalComplexity || 'medium',
    budget: input.budget || null,
    timeline: input.timeline?.trim().toLowerCase() || null
  }
  
  const inputString = JSON.stringify(normalizedInput)
  return crypto.createHash('sha256').update(inputString).digest('hex')
}
