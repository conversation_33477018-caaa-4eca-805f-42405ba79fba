"use client"

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@supabase/ssr'
import { type User } from '@/lib/services/user-management-service'

export interface CurrentUser extends User {
  isAdmin: boolean
  isModerator: boolean
  canManageUsers: boolean
}

export function useCurrentUser() {
  const [user, setUser] = useState<CurrentUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  useEffect(() => {
    let mounted = true

    const getCurrentUser = async () => {
      try {
        setLoading(true)
        setError(null)

        // Get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        if (sessionError) {
          throw sessionError
        }

        if (!session?.user) {
          if (mounted) {
            setUser(null)
            setLoading(false)
          }
          return
        }

        // Get user data from our users table
        const { data, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()

        if (userError) {
          // If user doesn't exist in our users table, create a basic user object
          if (userError.code === 'PGRST116') {
            const basicUser: CurrentUser = {
              id: session.user.id,
              email: session.user.email || '',
              name: session.user.user_metadata?.name || null,
              avatar_url: session.user.user_metadata?.avatar_url || null,
              status: 'active',
              role: 'user',
              email_verified: session.user.email_confirmed_at ? true : false,
              created_at: session.user.created_at,
              updated_at: session.user.updated_at || session.user.created_at,
              isAdmin: false,
              isModerator: false,
              canManageUsers: false,
              // No created_by info for users not in our database yet
              created_by: undefined,
              created_by_email: undefined,
              created_by_name: undefined,
            }
            
            if (mounted) {
              setUser(basicUser)
              setLoading(false)
            }
            return
          }
          throw userError
        }

        // Create enhanced user object with permission flags
        const enhancedUser: CurrentUser = {
          ...data,
          isAdmin: data.role === 'admin',
          isModerator: data.role === 'moderator',
          canManageUsers: data.role === 'admin' || data.role === 'moderator',
        }

        if (mounted) {
          setUser(enhancedUser)
          setLoading(false)
        }
      } catch (err: any) {
        console.error('Error getting current user:', err)
        if (mounted) {
          setError(err.message || 'Failed to get user information')
          setUser(null)
          setLoading(false)
        }
      }
    }

    // Get initial user
    getCurrentUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_OUT') {
          if (mounted) {
            setUser(null)
            setLoading(false)
          }
        } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          await getCurrentUser()
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [supabase])

  return {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    isAdmin: user?.isAdmin || false,
    isModerator: user?.isModerator || false,
    canManageUsers: user?.canManageUsers || false,
  }
}
