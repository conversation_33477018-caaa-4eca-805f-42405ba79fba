# SaaS Ideas Design System

## Overview

This design system ensures consistency, accessibility, and maintainability across the SaaS Ideas platform. It provides a unified language for designers and developers to create cohesive user experiences.

## How to Use This Documentation

This documentation is organized into two main sections:

1. **UI Components**: Reusable interface elements with visual examples, code snippets, usage guidelines, and props documentation.
2. **Styles**: Design tokens, patterns, and guidelines that define the visual language of the platform.

Each component includes:
- Visual examples
- Code snippets
- Usage guidelines
- Props documentation
- Accessibility considerations

## UI Components

### Navigation Components

- **Sidebar Navigation**: Primary navigation component used throughout the application
- **Breadcrumbs**: Secondary navigation showing hierarchical path
- **Tabs**: Content organization within sections
- **Pagination**: Navigation through multi-page content

### Form Components

- **Text Input**: Standard text input fields
- **Select**: Dropdown selection components
- **Checkbox & Radio**: Selection controls
- **Toggle Switch**: Binary state controls
- **Date Picker**: Date selection components
- **File Upload**: File upload interfaces

### Layout Components

- **Card**: Content containers with consistent styling
- **Modal**: Overlay dialogs for focused interactions
- **Drawer**: Side panel for secondary content
- **Accordion**: Expandable content sections
- **Divider**: Visual separators

### Feedback Components

- **Alert**: Informational messages
- **Toast**: Temporary notifications
- **Progress**: Loading and progress indicators
- **Skeleton**: Content loading placeholders

### Data Display Components

- **Table**: Structured data display
- **List**: Vertical arrangement of items
- **Badge**: Status indicators
- **Avatar**: User representations
- **Tooltip**: Contextual information

### Specialized Components

- **Kanban Board**: Drag-and-drop task management
- **User Flow Diagram**: Interactive flow creation tools
- **AI Insight Cards**: AI-generated information displays

### Animation Components

For enhanced user experience, we incorporate a collection of animation components from [reactbits.dev](https://reactbits.dev). These components are documented in detail in the [Reactbits Components Documentation](./reactbits-components/index.md).

Key animation categories include:

- [**Text Animations**](./reactbits-components/text-animations-index.md): Enhance typography with dynamic effects
- **Motion Components**: Add movement to UI elements
- **Transition Effects**: Create smooth transitions between states
- **Hover Effects**: Provide interactive feedback

## Styles

### Design Tokens

Our design system uses CSS variables and Tailwind CSS to implement consistent design tokens:

#### Colors

```css
:root {
  /* Primary palette */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;
  --color-primary-950: #082f49;
  
  /* Neutral palette */
  --color-neutral-50: #f8fafc;
  --color-neutral-100: #f1f5f9;
  --color-neutral-200: #e2e8f0;
  --color-neutral-300: #cbd5e1;
  --color-neutral-400: #94a3b8;
  --color-neutral-500: #64748b;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1e293b;
  --color-neutral-900: #0f172a;
  --color-neutral-950: #020617;
  
  /* Semantic colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
}
```

#### Typography

```css
:root {
  /* Font families */
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'Fira Code', monospace;
  
  /* Font sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  
  /* Font weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Line heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}
```

#### Spacing

```css
:root {
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1-5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2-5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3-5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-32: 8rem;
}
```

### Design Patterns

#### Layout Patterns

- **Container**: Centered content with responsive max-width
- **Grid System**: Flexible 12-column grid
- **Stack**: Vertical spacing between elements
- **Cluster**: Horizontal grouping with wrapping

#### Interaction Patterns

- **Button States**: Normal, hover, active, focus, disabled
- **Form Validation**: Input validation and error states
- **Loading States**: Skeleton loaders and spinners
- **Empty States**: Placeholders for empty content

### Layout Guidelines

- **Responsive Breakpoints**:
  - Mobile: < 640px
  - Tablet: 640px - 1024px
  - Desktop: > 1024px
  
- **Spacing System**:
  - Based on 4px grid (0.25rem)
  - Consistent spacing between elements
  - Responsive spacing at different breakpoints

## Best Practices

### Accessibility

- **Color Contrast**: Maintain WCAG AA compliance (minimum 4.5:1 for normal text)
- **Keyboard Navigation**: Ensure all interactive elements are keyboard accessible
- **Screen Reader Support**: Use semantic HTML and ARIA attributes
- **Focus Management**: Visible focus indicators for keyboard users
- **Reduced Motion**: Respect user preferences for reduced motion

### Performance

- **Component Lazy Loading**: Load components only when needed
- **Image Optimization**: Use responsive images and modern formats
- **Animation Performance**: Use GPU-accelerated properties for animations
- **Bundle Size**: Monitor and optimize component bundle size

## Contributing to the Design System

### Adding or Modifying Components

1. **Research**: Identify the need for a new component or modification
2. **Design**: Create designs using our design tokens
3. **Documentation**: Document the component's purpose, usage, and props
4. **Implementation**: Build the component following our coding standards
5. **Review**: Submit for design and code review
6. **Testing**: Ensure accessibility and cross-browser compatibility
7. **Release**: Add to the design system with version notes

### Documentation Standards

- Use clear, concise language
- Include visual examples
- Provide complete props documentation
- Include accessibility considerations
- Show usage examples with code snippets

## Version History

| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 2023-06-01 | Initial design system release |
| 1.1.0 | 2023-08-15 | Added dark mode support |
| 1.2.0 | 2023-10-20 | Added specialized components for Kanban and User Flow |
| 1.3.0 | 2023-12-05 | Improved accessibility across all components |
| 1.4.0 | 2024-02-10 | Added animation components from reactbits.dev | 