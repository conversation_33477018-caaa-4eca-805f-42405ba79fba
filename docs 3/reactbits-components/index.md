# Reactbits Components Documentation

This documentation covers the collection of components from reactbits.dev that can be used to enhance your React application UI.

## Component Categories

### Text and Typography

- [**Text Animations**](./text-animations-index.md) - Components for animating text elements
  - [SplitText](./text-animations.md#splittext) - Animate individual characters or words
  - [RotatingText](./text-animations.md#rotatingtext) - Cycle through multiple text phrases
  - [FuzzyText](./text-animations-2.md#fuzzytext) - Create glitchy text effects
  - [CountUp](./text-animations-2.md#countup) - Animate counting numbers
  - [TextCursor](./text-animations-2.md#textcursor) - Text that follows cursor movement
  - [ScrollFloat](./text-animations-3.md#scrollfloat-component) - Text that floats in on scroll

### Layout Components

- [**Grids and Containers**](./layout-components.md) - Flexible layout components
  - AutoGrid - Responsive grid with automatic sizing
  - MasonryGrid - Pinterest-style masonry layout
  - AspectRatioBox - Maintain aspect ratio for content
  - StickyContainer - Create sticky elements with options

- [**Dividers and Spacers**](./dividers.md) - Components for visual separation
  - FancyDivider - Customizable divider with various styles
  - ResponsiveSpacer - Space that adjusts to viewport size
  - DividerWithText - Divider with centered text

### Animation and Interaction

- [**Motion Components**](./motion-components.md) - General purpose animation components
  - FadeIn - Fade elements in on mount or scroll
  - SlideIn - Slide elements in from different directions
  - ScaleIn - Scale elements in with customizable options
  - Parallax - Create parallax scrolling effects

- [**Hover Effects**](./hover-effects.md) - Interactive hover animations
  - HoverCard - Card with hover animations
  - HoverGlow - Adds glow effect on hover
  - HoverTilt - Tilts elements on hover

- [**Transition Effects**](./transition-effects.md) - Page and section transitions
  - PageTransition - Smooth transitions between pages
  - SectionTransition - Transitions between sections

### UI Elements

- [**Cards and Containers**](./cards.md) - Various card components
  - GlassCard - Frosted glass effect card
  - FeatureCard - Card for highlighting features
  - ProfileCard - Card for user profiles
  - StatCard - Card for displaying statistics

- [**Buttons and Controls**](./buttons.md) - Interactive button components
  - GlowButton - Button with glow effect
  - RippleButton - Button with ripple animation
  - IconButton - Button with integrated icon
  - ToggleSwitch - Custom toggle switch component

- [**Form Components**](./forms.md) - Enhanced form elements
  - FloatingLabelInput - Input with floating label
  - TagInput - Input for creating tags
  - RangeSlider - Custom range slider
  - SearchInput - Enhanced search input

### Data Display

- [**Charts and Graphs**](./charts.md) - Data visualization components
  - LineChart - Animated line chart
  - BarChart - Customizable bar chart
  - PieChart - Interactive pie chart
  - RadarChart - Radar/spider chart

- [**Data Tables**](./tables.md) - Enhanced table components
  - SortableTable - Table with sorting capabilities
  - FilterTable - Table with filtering options
  - PaginatedTable - Table with pagination
  - ResponsiveTable - Mobile-friendly table

### Feedback and Status

- [**Loaders and Spinners**](./loaders.md) - Loading indicators
  - CircleLoader - Circular loading animation
  - ProgressBar - Customizable progress bar
  - SkeletonLoader - Content placeholder while loading
  - LoadingDots - Simple dots loading animation

- [**Notifications**](./notifications.md) - User notification components
  - Toast - Toast notification system
  - AlertBanner - Banner for important alerts
  - StatusIndicator - Show status with colors and icons

## Installation

Most components require specific dependencies. Each component's documentation includes its specific installation requirements. Common dependencies include:

```bash
# Core animation libraries
npm install framer-motion gsap

# Utility libraries
npm install clsx tailwind-merge

# For chart components
npm install d3 react-spring
```

## Usage Guidelines

### Getting Started

1. Browse the component categories above to find what you need
2. Check the specific component documentation for installation requirements
3. Follow the usage examples to implement the component in your project
4. Customize the component using the available props

### Best Practices

- Start with the simplest version of a component and add complexity as needed
- Consider performance implications when using multiple animated components
- Test components across different devices and screen sizes
- Respect accessibility concerns, especially with animations and interactive elements

## Contributing

If you'd like to contribute to this documentation or suggest improvements to the components, please see our [contribution guidelines](./contributing.md).

## License

All components are available under the MIT License. See individual component documentation for any specific licensing information. 