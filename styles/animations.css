/* Glass card effect */
.glass-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease-in-out;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(to right, #60A5FA, #7C3AED);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Glow effect */
.glow {
  position: relative;
}

.glow::after {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(to right, #60A5FA, #7C3AED);
  filter: blur(15px);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.glow:hover::after {
  opacity: 0.5;
} 