@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles below */

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Custom utilities for consistent card heights and better responsiveness */
  .equal-height {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .flex-grow-1 {
    flex-grow: 1;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 to-purple-400 text-transparent bg-clip-text;
  }
  
  /* Glow effects */
  .hover\:glow-blue:hover {
    box-shadow: 0 0 15px 2px rgba(59, 130, 246, 0.3);
  }
  
  .hover\:glow-purple:hover {
    box-shadow: 0 0 15px 2px rgba(168, 85, 247, 0.3);
  }
  
  .hover\:glow-emerald:hover {
    box-shadow: 0 0 15px 2px rgba(16, 185, 129, 0.3);
  }
  
  .hover\:glow-amber:hover {
    box-shadow: 0 0 15px 2px rgba(245, 158, 11, 0.3);
  }

  /* Credit card chip styles */
  .card-chip {
    @apply relative w-12 h-9 bg-yellow-400/90 rounded-md overflow-hidden;
    background-image: 
      linear-gradient(90deg, transparent 50%, rgba(255, 255, 255, 0.2) 50%),
      linear-gradient(180deg, transparent 50%, rgba(255, 255, 255, 0.2) 50%);
    background-size: 4px 100%, 100% 4px;
  }

  .card-chip::before {
    content: '';
    @apply absolute inset-0.5 bg-yellow-500/80 rounded-sm;
    background-image: 
      linear-gradient(90deg, transparent 50%, rgba(255, 255, 255, 0.1) 50%),
      linear-gradient(180deg, transparent 50%, rgba(255, 255, 255, 0.1) 50%);
    background-size: 2px 100%, 100% 2px;
  }
}

@layer components {
  /* Consistent card styling */
  .feature-card {
    @apply h-full flex flex-col bg-black/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/10;
    transition: all 0.3s ease;
  }
  
  .feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }
  
  .pricing-card {
    @apply h-full flex flex-col rounded-2xl p-6 sm:p-8 shadow-lg border transition-all duration-300 relative;
    background-image: 
      radial-gradient(circle at 100% 100%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
      linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, transparent 50%),
      repeating-linear-gradient(45deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.03) 1px, transparent 1px, transparent 4px);
    background-size: 100% 100%, 100% 100%, 100px 100px;
    background-position: center;
  }

  .pricing-card::before {
    content: '';
    @apply absolute inset-0 rounded-2xl opacity-20;
    background: 
      linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) 50%/200% 100%;
    animation: shimmer 3s infinite;
  }

  .pricing-card-featured {
    @apply bg-gradient-to-br from-blue-700/80 to-purple-700/80 border-2 border-blue-500 md:scale-105 md:-my-2 hover:shadow-blue-500/20 hover:shadow-lg;
    background-image: 
      radial-gradient(circle at 100% 100%, rgba(255, 255, 255, 0.12) 0%, transparent 25%),
      linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
      repeating-linear-gradient(45deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 1px, transparent 1px, transparent 4px);
    background-size: 100% 100%, 100% 100%, 100px 100px;
    background-position: center;
  }
  
  .pricing-card-regular {
    @apply bg-slate-900/70 hover:bg-slate-900/90 hover:shadow-lg;
  }

  .pricing-card-header {
    @apply flex items-start justify-between mb-8;
  }

  .pricing-card-chip-row {
    @apply flex items-center gap-4 mb-6;
  }

  .pricing-card-network {
    @apply text-xs font-mono tracking-wider opacity-50;
  }

  .pricing-card-details {
    @apply mt-auto pt-4 border-t border-white/10;
  }

  .pricing-card-number {
    @apply font-mono tracking-wider text-lg opacity-80 mb-4;
  }

  .pricing-card-footer {
    @apply flex items-center justify-between text-xs font-mono tracking-wider opacity-60;
  }
  
  .stat-card {
    @apply equal-height bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800/50 overflow-hidden transition-all duration-300;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* Responsive text sizing */
  h1 {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold;
  }
  
  h2 {
    @apply text-2xl sm:text-3xl md:text-4xl font-bold;
  }
  
  h3 {
    @apply text-xl sm:text-2xl font-semibold;
  }
  
  p {
    @apply text-base sm:text-lg;
  }
}

/* Force cache refresh for styling changes */
html {
  font-display: swap;
  scroll-behavior: auto;
}

/* Workflow Connector Animations */
@keyframes dash {
  to {
    stroke-dashoffset: -12;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

.animate-dash {
  animation: dash 1.5s linear infinite;
}

/* Connector Glow Effect */
.connector-glow {
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.4));
  transition: filter 0.3s ease;
}

.connector-glow:hover {
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.6));
}

/* Connection Point Animations */
.connection-point {
  animation: pulse 2s ease-in-out infinite;
  transform-origin: center;
  transition: all 0.3s ease;
}

.connection-point:hover {
  animation-play-state: paused;
  transform: scale(1.3);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

