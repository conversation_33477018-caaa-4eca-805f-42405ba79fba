#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Define source and destination paths
const sourceDir = path.join(process.cwd(), 'screenshots');
const publicDir = path.join(process.cwd(), 'public');
const demoDir = path.join(publicDir, 'images', 'demo');

// Ensure directories exist
if (!fs.existsSync(demoDir)) {
  fs.mkdirSync(demoDir, { recursive: true });
  console.log('Created directory:', demoDir);
}

// Define the screenshots mapping
const screenshots = [
  {
    name: 'login.png',
    dest: path.join(publicDir, 'login.png'),
    description: 'Login screen'
  },
  {
    name: 'signup.png',
    dest: path.join(demoDir, 'signup.png'),
    description: 'Signup screen'
  },
  {
    name: 'dashboard.png',
    dest: path.join(demoDir, 'dashboard.png'),
    description: 'Dashboard'
  },
  {
    name: 'ai-insights.png',
    dest: path.join(demoDir, 'ai-insights.png'),
    description: 'AI Insights'
  },
  {
    name: 'idea-validator.png',
    dest: path.join(demoDir, 'idea-validator.png'),
    description: 'Idea Validator'
  },
  {
    name: 'settings.png',
    dest: path.join(demoDir, 'settings.png'),
    description: 'Settings'
  }
];

// Instructions for the user
console.log(`
To use your actual screenshots:

1. Create a 'screenshots' folder in the project root directory
2. Save the following screenshots in that folder:
   ${screenshots.map(s => `- ${s.name}: ${s.description}`).join('\n   ')}
3. Run this script again to copy them to the correct locations

For now, placeholder images have been created for the demonstration.
`);

// Check if screenshots directory exists
if (fs.existsSync(sourceDir)) {
  console.log('Found screenshots directory, checking for images...');
  
  // Copy each screenshot if it exists
  screenshots.forEach(screenshot => {
    const sourcePath = path.join(sourceDir, screenshot.name);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, screenshot.dest);
      console.log(`Copied ${screenshot.name} to ${screenshot.dest}`);
    } else {
      console.log(`Screenshot ${screenshot.name} not found in screenshots directory`);
    }
  });
} else {
  console.log('Screenshots directory not found. Using placeholder images.');
} 