const fs = require('fs');
const path = require('path');
const https = require('https');

// Create videos directory if it doesn't exist
const videosDir = path.join(__dirname, '../public/videos');
if (!fs.existsSync(videosDir)) {
  fs.mkdirSync(videosDir, { recursive: true });
  console.log('Created videos directory');
}

// List of placeholder videos to download
const videos = [
  {
    name: 'idea-validation.mp4',
    url: 'https://assets.mixkit.co/videos/preview/mixkit-typing-on-a-keyboard-with-a-data-analysis-on-screen-9736-large.mp4'
  },
  {
    name: 'user-flow.mp4',
    url: 'https://assets.mixkit.co/videos/preview/mixkit-man-working-on-his-laptop-at-home-4807-large.mp4'
  },
  {
    name: 'project-management.mp4',
    url: 'https://assets.mixkit.co/videos/preview/mixkit-woman-typing-on-a-laptop-4789-large.mp4'
  },
  {
    name: 'launch-success.mp4',
    url: 'https://assets.mixkit.co/videos/preview/mixkit-happy-man-working-on-a-laptop-4809-large.mp4'
  }
];

// Download each video
videos.forEach(video => {
  const filePath = path.join(videosDir, video.name);
  
  // Skip if file already exists
  if (fs.existsSync(filePath)) {
    console.log(`${video.name} already exists, skipping...`);
    return;
  }
  
  console.log(`Downloading ${video.name}...`);
  
  const file = fs.createWriteStream(filePath);
  https.get(video.url, response => {
    response.pipe(file);
    
    file.on('finish', () => {
      file.close();
      console.log(`Downloaded ${video.name}`);
    });
  }).on('error', err => {
    fs.unlink(filePath, () => {}); // Delete the file if there's an error
    console.error(`Error downloading ${video.name}: ${err.message}`);
  });
});

console.log('Script complete. Videos will download in the background.'); 