#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Path to save the login image
const loginImagePath = path.join(process.cwd(), 'public', 'login.png');

console.log(`
The login screenshot has been saved as a placeholder.

To use the actual login screenshot:
1. Copy the login screenshot image to: ${loginImagePath}
2. The image will automatically be used in the demonstration
`);

// Check if the image exists
if (fs.existsSync(loginImagePath)) {
  console.log('Login image already exists at:', loginImagePath);
} else {
  console.log('Login image not found. Please save it to:', loginImagePath);
} 