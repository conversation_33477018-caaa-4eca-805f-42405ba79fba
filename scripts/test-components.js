#!/usr/bin/env node

console.log('Testing components...');

// Test if required dependencies are installed
try {
  require('framer-motion');
  console.log('✅ framer-motion is installed');
} catch (error) {
  console.error('❌ framer-motion is not installed. Please run: npm install framer-motion');
}

try {
  require('lucide-react');
  console.log('✅ lucide-react is installed');
} catch (error) {
  console.error('❌ lucide-react is not installed. Please run: npm install lucide-react');
}

// Check if the component files exist
const fs = require('fs');
const path = require('path');

const componentsToCheck = [
  'components/LiveUserFlowModel.tsx',
  'components/UserFlowDiagram.tsx'
];

componentsToCheck.forEach(componentPath => {
  if (fs.existsSync(path.join(process.cwd(), componentPath))) {
    console.log(`✅ ${componentPath} exists`);
  } else {
    console.error(`❌ ${componentPath} does not exist`);
  }
});

console.log('All tests completed!'); 