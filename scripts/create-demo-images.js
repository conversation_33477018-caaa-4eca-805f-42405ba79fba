#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');

// Create demo images directory if it doesn't exist
const demoDir = path.join(process.cwd(), 'public', 'images', 'demo');
if (!fs.existsSync(demoDir)) {
  fs.mkdirSync(demoDir, { recursive: true });
  console.log('Created directory:', demoDir);
}

// List of demo images to create
const demoImages = [
  {
    name: 'login.png',
    url: 'https://placehold.co/1200x800/1e293b/ffffff?text=SaaSifyX+Login+Screen'
  },
  {
    name: 'signup.png',
    url: 'https://placehold.co/1200x800/1e293b/ffffff?text=SaaSifyX+Signup+Screen'
  },
  {
    name: 'dashboard.png',
    url: 'https://placehold.co/1200x800/1e293b/ffffff?text=SaaSifyX+Dashboard'
  },
  {
    name: 'ai-insights.png',
    url: 'https://placehold.co/1200x800/1e293b/ffffff?text=AI+Insights'
  },
  {
    name: 'idea-validator.png',
    url: 'https://placehold.co/1200x800/1e293b/ffffff?text=Idea+Validator'
  },
  {
    name: 'settings.png',
    url: 'https://placehold.co/1200x800/1e293b/ffffff?text=Settings'
  }
];

// Download images
demoImages.forEach(image => {
  const filePath = path.join(demoDir, image.name);
  
  // Skip if file already exists
  if (fs.existsSync(filePath)) {
    console.log(`${image.name} already exists, skipping...`);
    return;
  }
  
  console.log(`Downloading ${image.name}...`);
  
  const file = fs.createWriteStream(filePath);
  https.get(image.url, response => {
    response.pipe(file);
    file.on('finish', () => {
      file.close();
      console.log(`Downloaded ${image.name}`);
    });
  }).on('error', err => {
    fs.unlink(filePath, () => {}); // Delete the file if there's an error
    console.error(`Error downloading ${image.name}:`, err.message);
  });
});

console.log('Demo images setup complete!'); 