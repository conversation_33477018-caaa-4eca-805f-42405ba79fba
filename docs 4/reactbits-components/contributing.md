# Contributing to Reactbits Components Documentation

Thank you for your interest in contributing to our documentation! This guide will help you understand how to add or update component documentation in a consistent manner.

## Getting Started

### Prerequisites

- Basic knowledge of Markdown syntax
- Understanding of React and TypeScript
- Familiarity with the component you're documenting

### Documentation Structure

Each component should be documented following our standard template and organization:

1. **Component Category**: Determine which category your component belongs to (text animations, motion, layout, etc.)
2. **Documentation File**: Create or update the appropriate markdown file
3. **Index Updates**: Update the category index and main index files

## Step-by-Step Process

### 1. Use the Template

Start by copying our [component template](../templates/component-template.md) as a base for your documentation.

### 2. Fill in Component Details

Complete each section of the template:

- **Overview**: Provide a clear, concise description of the component
- **Best Used For**: List 3-5 specific use cases
- **Installation**: List all required dependencies with installation commands
- **Usage**: Show a basic implementation example
- **Props**: Document all props in the table format
- **Variants**: If applicable, document different component variants
- **Examples**: Provide at least two usage examples
- **Implementation**: Include the full component code
- **Accessibility**: Document accessibility considerations
- **Browser Compatibility**: Note any browser-specific issues
- **Performance**: Document performance considerations
- **Related Components**: Link to related components
- **Changelog**: Track version changes

### 3. Code Examples

For code examples:

- Use syntax highlighting with `jsx` or `tsx` language specifiers
- Include imports in your examples
- Show realistic prop values
- Comment complex code sections

### 4. Testing Your Documentation

Before submitting:

- Check all markdown syntax for correctness
- Verify that code examples are functional
- Ensure all links work properly
- Check for spelling and grammar errors

### 5. Update Index Files

After creating your component documentation:

1. Add your component to the appropriate category index file
2. If it's a new category, create a new category index file
3. Update the main [index.md](./index.md) file to include your component

Example entry for a category index:

```markdown
| Component | Description | Dependencies | Best For |
|-----------|-------------|--------------|----------|
| [YourComponent](./your-component.md#yourcomponent) | Brief description | Required packages | Primary use cases |
```

### 6. Submit Your Contribution

Once you've completed your documentation:

1. Ensure all files are properly formatted
2. Submit your changes following our project's contribution workflow
3. Be prepared to address feedback and make revisions

## Documentation Style Guide

### Markdown Formatting

- Use ATX-style headers (`#` for h1, `##` for h2, etc.)
- Use fenced code blocks with language specifiers
- Use tables for structured data like props
- Use bullet points for lists
- Use numbered lists for sequential steps

### Writing Style

- Be clear and concise
- Use present tense
- Address the reader directly
- Avoid jargon when possible
- Explain technical terms when necessary
- Use consistent terminology

### Code Style

- Follow our project's TypeScript coding standards
- Include type definitions
- Use meaningful variable names
- Include comments for complex logic
- Follow accessibility best practices

## Best Practices

- **Completeness**: Document all props and features
- **Accuracy**: Ensure code examples work as described
- **Consistency**: Follow the established documentation pattern
- **Clarity**: Make explanations easy to understand
- **Examples**: Provide realistic, useful examples
- **Accessibility**: Always include accessibility considerations

## Need Help?

If you have questions about contributing to our documentation:

- Check existing documentation for examples
- Refer to the [component template](../templates/component-template.md)
- Contact the documentation team for guidance

Thank you for helping improve our component documentation! 