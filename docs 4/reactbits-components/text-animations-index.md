# Text Animation Components

This documentation covers various text animation components from reactbits.dev that can enhance your UI with dynamic text effects.

## Overview

Text animations add visual interest and improve user engagement by drawing attention to important content. These components can be used across your application to create a more dynamic and interactive experience.

## Components

| Component | Description | Dependencies | Best For |
|-----------|-------------|--------------|----------|
| [SplitText](./text-animations.md#splittext) | Animates individual characters or words of text | GSAP | Headings, titles, emphasis |
| [RotatingText](./text-animations.md#rotatingtext) | Cycles through multiple text phrases | framer-motion | Feature lists, taglines |
| [FuzzyText](./text-animations-2.md#fuzzytext) | Creates glitchy, fuzzy text effect | None | Error pages, creative effects |
| [CountUp](./text-animations-2.md#countup) | Animates numbers counting up or down | framer-motion | Statistics, metrics, dashboards |
| [TextCursor](./text-animations-2.md#textcursor) | Creates text that follows cursor movement | framer-motion | Interactive elements, playful UIs |
| [ScrollFloat](./text-animations-3.md#scrollfloat-component) | Text that floats in on scroll | GSAP | Section headings, landing pages |

## Installation Requirements

Different components require different dependencies. Here's a summary:

```bash
# For GSAP-based components (SplitText, ScrollFloat)
npm install gsap

# For framer-motion-based components (RotatingText, CountUp, TextCursor)
npm install framer-motion
```

## Usage Guidelines

### When to Use Text Animations

- **Sparingly**: Overusing animations can distract users and make your UI feel busy
- **Purposefully**: Use animations to direct attention to important content
- **Consistently**: Maintain a consistent animation style throughout your application
- **Accessibly**: Ensure animations respect user preferences (reduced motion settings)

### Performance Considerations

- Text animations can impact performance, especially on lower-end devices
- Consider using the `once` prop when available to only trigger animations once
- For scroll-triggered animations, use appropriate thresholds to avoid triggering too many at once
- Test animations on various devices to ensure smooth performance

### Accessibility

Some users may prefer reduced motion. Implement a system that respects the user's preferences:

```tsx
// Example of checking for reduced motion preference
const prefersReducedMotion = 
  typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

// Then pass this to components that support disabling animations
<CountUp 
  to={1000} 
  duration={prefersReducedMotion ? 0 : 2} 
/>
```

## Examples

### Combining Multiple Text Animations

```tsx
function HeroSection() {
  return (
    <div className="hero-section py-20">
      <ScrollFloat direction="up" className="mb-4">
        <SplitText>Welcome to our platform</SplitText>
      </ScrollFloat>
      
      <ScrollFloat direction="up" delay={0.3} className="mb-8">
        <RotatingText 
          texts={["Innovate", "Create", "Collaborate"]} 
          duration={2}
        />
      </ScrollFloat>
      
      <div className="stats-row flex gap-8 justify-center">
        <div className="stat">
          <p>Users</p>
          <CountUp to={10000} separator="," />
        </div>
        <div className="stat">
          <p>Projects</p>
          <CountUp to={5280} separator="," />
        </div>
      </div>
    </div>
  );
}
```

## Related Components

- [Motion Components](./motion-components.md) - General purpose animation components
- [Transition Effects](./transition-effects.md) - Page and section transition animations
- [Hover Effects](./hover-effects.md) - Interactive hover animations 