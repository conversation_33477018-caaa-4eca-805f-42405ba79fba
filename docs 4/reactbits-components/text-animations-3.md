# ScrollFloat Component

This documentation covers the ScrollFloat component from reactbits.dev, which creates text that floats in and out as the user scrolls.

## Overview
The ScrollFloat component creates a smooth floating animation effect for text elements that is triggered by scrolling. Text can float in from different directions with customizable timing and easing.

## Best Used For
- Section headings
- Feature introductions
- Landing page text elements
- Testimonial quotes
- Call-to-action phrases

## Installation
```bash
npm install gsap
```

## Usage
```jsx
import ScrollFloat from './components/ScrollFloat';

function LandingPage() {
  return (
    <div className="landing-section" style={{ height: '100vh' }}>
      <ScrollFloat 
        direction="up" 
        distance={50} 
        delay={0.2} 
        className="text-4xl font-bold"
      >
        Welcome to our platform
      </ScrollFloat>
      
      <ScrollFloat 
        direction="left" 
        distance={100} 
        delay={0.4}
        className="text-xl mt-4"
      >
        Discover amazing features
      </ScrollFloat>
    </div>
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| children | ReactNode | (required) | Text content to display |
| direction | "up" \| "down" \| "left" \| "right" | "up" | Direction of floating animation |
| distance | number | 50 | Distance to travel in pixels |
| delay | number | 0 | Delay before animation starts (seconds) |
| duration | number | 1 | Duration of animation (seconds) |
| ease | string | "power2.out" | GSAP easing function |
| className | string | "" | Additional CSS classes |
| once | boolean | true | Whether to play animation only once |
| threshold | number | 0.1 | Intersection threshold to trigger animation |

## Implementation

```tsx
import React, { useRef, useEffect } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Make sure to register ScrollTrigger with GSAP
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface ScrollFloatProps {
  children: React.ReactNode;
  direction?: "up" | "down" | "left" | "right";
  distance?: number;
  delay?: number;
  duration?: number;
  ease?: string;
  className?: string;
  once?: boolean;
  threshold?: number;
}

const ScrollFloat: React.FC<ScrollFloatProps> = ({
  children,
  direction = "up",
  distance = 50,
  delay = 0,
  duration = 1,
  ease = "power2.out",
  className = "",
  once = true,
  threshold = 0.1,
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    const trigger = triggerRef.current;

    if (!element || !trigger) return;

    // Set initial state based on direction
    const initialProps: gsap.TweenVars = {
      autoAlpha: 0,
    };

    switch (direction) {
      case "up":
        initialProps.y = distance;
        break;
      case "down":
        initialProps.y = -distance;
        break;
      case "left":
        initialProps.x = distance;
        break;
      case "right":
        initialProps.x = -distance;
        break;
    }

    gsap.set(element, initialProps);

    // Create animation
    const animation = gsap.to(element, {
      autoAlpha: 1,
      x: 0,
      y: 0,
      duration,
      delay,
      ease,
      paused: true,
    });

    // Create scroll trigger
    const scrollTrigger = ScrollTrigger.create({
      trigger: trigger,
      start: `top bottom-=${threshold * 100}%`,
      onEnter: () => animation.play(),
      onLeaveBack: () => !once && animation.reverse(),
      markers: false,
    });

    return () => {
      animation.kill();
      scrollTrigger.kill();
    };
  }, [direction, distance, delay, duration, ease, once, threshold]);

  return (
    <div ref={triggerRef} className="scroll-float-trigger">
      <div ref={elementRef} className={`scroll-float ${className}`}>
        {children}
      </div>
    </div>
  );
};

export default ScrollFloat;
```

## Examples

### Staggered Text Entries

```jsx
function StaggeredHeadings() {
  return (
    <div className="space-y-8 py-20">
      <ScrollFloat direction="up" delay={0} className="text-4xl font-bold">
        First Heading
      </ScrollFloat>
      
      <ScrollFloat direction="up" delay={0.2} className="text-2xl">
        Supporting text floats in slightly later
      </ScrollFloat>
      
      <ScrollFloat direction="up" delay={0.4} className="text-xl">
        And this comes in last
      </ScrollFloat>
    </div>
  );
}
```

### Different Directions

```jsx
function DirectionalExample() {
  return (
    <div className="grid grid-cols-2 gap-8 py-20">
      <ScrollFloat direction="left" className="text-xl">
        Floating from the right
      </ScrollFloat>
      
      <ScrollFloat direction="right" className="text-xl">
        Floating from the left
      </ScrollFloat>
      
      <ScrollFloat direction="up" className="text-xl">
        Floating from below
      </ScrollFloat>
      
      <ScrollFloat direction="down" className="text-xl">
        Floating from above
      </ScrollFloat>
    </div>
  );
}
```

## Usage Guidelines

- For the best effect, use ScrollFloat on elements that will be scrolled into view
- Consider using different delay values for related elements to create a staggered effect
- Adjust the distance prop based on screen size - smaller distances work better on mobile
- Use the threshold prop to control when the animation triggers relative to the viewport
- For longer text blocks, consider splitting them into multiple ScrollFloat components 