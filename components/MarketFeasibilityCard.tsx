"use client"

import React from 'react'
import { motion } from 'framer-motion'

interface MarketFeasibilityCardProps {
  className?: string
}

export default function MarketFeasibilityCard({ className = '' }: MarketFeasibilityCardProps) {
  return (
    <div className={`rounded-xl bg-slate-900/80 backdrop-blur-sm border border-white/10 overflow-hidden shadow-xl ${className}`}>
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h3 className="text-2xl font-bold text-white">Market Feasibility</h3>
          </div>
          <div className="text-4xl font-bold text-blue-500">
            8.2<span className="text-lg text-slate-400">/10</span>
          </div>
        </div>
        
        <div className="space-y-6">
          {[
            { name: 'Uniqueness', description: 'Market differentiation', value: 85, color: 'from-green-500 to-emerald-500' },
            { name: 'Stickiness', description: 'User retention potential', value: 92, color: 'from-blue-500 to-cyan-500' },
            { name: 'Growth', description: 'Scalability factor', value: 78, color: 'from-purple-500 to-violet-500' },
            { name: 'Pricing', description: 'Revenue optimization', value: 88, color: 'from-emerald-500 to-teal-500' }
          ].map((metric) => (
            <div key={metric.name} className="space-y-2">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-white font-medium">{metric.name}</h4>
                  <p className="text-xs text-slate-400">{metric.description}</p>
                </div>
                <span className="text-white font-bold">{metric.value}%</span>
              </div>
              <div className="h-2 w-full bg-slate-800 rounded-full overflow-hidden">
                <motion.div 
                  className={`h-full bg-gradient-to-r ${metric.color}`}
                  initial={{ width: 0 }}
                  animate={{ width: `${metric.value}%` }}
                  transition={{ duration: 1, delay: 0.3 }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 