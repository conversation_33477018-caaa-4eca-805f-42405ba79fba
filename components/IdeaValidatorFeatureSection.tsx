"use client"

import React from 'react'
import ScrollTriggeredFeature from './ScrollTriggeredFeature'
import { CheckCircle, DollarSign, Code, Users, Lightbulb, ShieldCheck } from 'lucide-react'

export default function IdeaValidatorFeatureSection() {
  return (
    <ScrollTriggeredFeature
      title="Idea Validation"
      description="Validate your SaaS idea with comprehensive market analysis, technical feasibility, and business viability checks before investing significant resources."
      imageSrc="/images/demo/idea-validator.png"
      imageAlt="SaaSifyX idea validator interface showing validation metrics and analysis"
      imagePosition="right"
      featurePoints={[
        {
          title: "Market Validation",
          description: "Analyze market size, competition, and demand to determine if your idea has a viable target audience.",
          icon: <CheckCircle className="text-green-500" size={20} />
        },
        {
          title: "Business Model Analysis",
          description: "Evaluate revenue projections and business model sustainability with our financial modeling tools.",
          icon: <DollarSign className="text-amber-500" size={20} />
        },
        {
          title: "Technical Feasibility",
          description: "Assess the technical complexity and resource requirements needed to build your SaaS solution.",
          icon: <Code className="text-blue-500" size={20} />
        },
        {
          title: "User Demand Analysis",
          description: "Gauge potential user interest and identify key user personas for your SaaS product.",
          icon: <Users className="text-purple-500" size={20} />
        },
        {
          title: "Innovation Assessment",
          description: "Evaluate the uniqueness and innovation factor of your idea compared to existing solutions.",
          icon: <Lightbulb className="text-cyan-500" size={20} />
        },
        {
          title: "Risk Evaluation",
          description: "Identify potential risks and challenges to prepare mitigation strategies early in development.",
          icon: <ShieldCheck className="text-rose-500" size={20} />
        }
      ]}
    />
  )
} 