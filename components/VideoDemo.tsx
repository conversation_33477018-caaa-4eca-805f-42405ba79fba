"use client"

import React, { useState } from 'react'

interface VideoDemoProps {
  className?: string
}

export default function VideoDemo({ className = '' }: VideoDemoProps) {
  const [isPlaying, setIsPlaying] = useState(false)

  return (
    <div className={`container mx-auto px-4 py-12 ${className}`}>
      <div className="text-center mb-12">
        <h2 className="text-3xl sm:text-4xl font-bold gradient-text mb-4">How SaaSifyX Works</h2>
        <p className="text-lg text-slate-300 max-w-2xl mx-auto">
          From idea to launch, our AI-powered platform guides you through every step of creating a successful SaaS product
        </p>
      </div>
      
      <div className="relative bg-slate-900/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 overflow-hidden shadow-xl">
        {/* Video container */}
        <div className="aspect-video rounded-lg overflow-hidden relative">
          <video 
            className="w-full h-full object-cover"
            src="/demo.mp4"
            poster="/demo-poster.jpg"
            controls={isPlaying}
            onClick={() => setIsPlaying(true)}
          >
            Your browser does not support the video tag.
          </video>
          
          {/* Play overlay - only show when video is not playing */}
          {!isPlaying && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-black/40 cursor-pointer"
              onClick={() => setIsPlaying(true)}
            >
              <div className="w-20 h-20 flex items-center justify-center rounded-full bg-blue-600/90 hover:bg-blue-600 transition-colors">
                <svg 
                  className="w-10 h-10 text-white" 
                  fill="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path d="M8 5v14l11-7z" />
                </svg>
              </div>
            </div>
          )}
        </div>
        
        {/* Video description */}
        <div className="mt-6 text-center">
          <p className="text-slate-300 text-sm">
            Watch how SaaSifyX helps you transform your SaaS idea into reality with AI-powered tools and guidance
          </p>
        </div>
      </div>
    </div>
  )
} 