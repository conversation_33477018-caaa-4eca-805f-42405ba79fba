"use client"

import React, { useState } from 'react'
import SaaSJourneyDashboard from './SaaSJourneyDashboard'
import VideoDemo from './VideoDemo'

interface HowItWorksSectionProps {
  className?: string
}

export default function HowItWorksSection({ className = '' }: HowItWorksSectionProps) {
  const [activeView, setActiveView] = useState<'dashboard' | 'video'>('dashboard')
  
  return (
    <div className={`w-full ${className}`}>
      <div className="container mx-auto px-4">
        {/* View toggle */}
        <div className="flex justify-center mb-8">
          <div className="inline-flex rounded-md shadow-sm" role="group">
            <button
              type="button"
              onClick={() => setActiveView('dashboard')}
              className={`px-4 py-2 text-sm font-medium rounded-l-lg ${
                activeView === 'dashboard' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-slate-800 text-slate-300 hover:bg-slate-700'
              }`}
            >
              SaaS Journey
            </button>
            <button
              type="button"
              onClick={() => setActiveView('video')}
              className={`px-4 py-2 text-sm font-medium rounded-r-lg ${
                activeView === 'video' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-slate-800 text-slate-300 hover:bg-slate-700'
              }`}
            >
              Video Demo
            </button>
          </div>
        </div>
        
        {/* Content based on active view */}
        <div className="relative">
          {activeView === 'dashboard' ? (
            <SaaSJourneyDashboard />
          ) : (
            <VideoDemo />
          )}
        </div>
      </div>
    </div>
  )
} 