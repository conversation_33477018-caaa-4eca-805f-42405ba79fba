"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { CheckCircle2 } from 'lucide-react'

interface SaaSJourneyFlowProps {
  className?: string
}

type JourneyStep = {
  id: string
  title: string
  description: string
  isCompleted: boolean
  isActive: boolean
}

export default function SaaSJourneyFlow({ className = '' }: SaaSJourneyFlowProps) {
  const [activeStep, setActiveStep] = useState(1)
  
  // Journey steps data
  const journeySteps: JourneyStep[] = [
    {
      id: 'ideate',
      title: 'Ideate',
      description: 'Start with your SaaS idea and let our AI analyze its potential',
      isCompleted: activeStep > 1,
      isActive: activeStep === 1
    },
    {
      id: 'analyze',
      title: 'Analyze',
      description: 'Get comprehensive AI-powered market analysis and feasibility scores',
      isCompleted: activeStep > 2,
      isActive: activeStep === 2
    },
    {
      id: 'design',
      title: 'Design',
      description: 'Create user flows and wireframes with our visual design tools',
      isCompleted: activeStep > 3,
      isActive: activeStep === 3
    },
    {
      id: 'plan',
      title: 'Plan',
      description: 'Organize development with AI-generated tasks and smart Kanban boards',
      isCompleted: activeStep > 4,
      isActive: activeStep === 4
    },
    {
      id: 'build',
      title: 'Build',
      description: 'Code efficiently with Cursor AI integration and premium development tools',
      isCompleted: activeStep > 5,
      isActive: activeStep === 5
    },
    {
      id: 'launch',
      title: 'Launch',
      description: 'Deploy your SaaS with confidence using our launch optimization features',
      isCompleted: activeStep > 6,
      isActive: activeStep === 6
    }
  ]
  
  // Auto-advance through steps
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => {
        if (prev >= journeySteps.length) {
          return 1
        }
        return prev + 1
      })
    }, 3000)
    
    return () => clearInterval(interval)
  }, [journeySteps.length])
  
  // Handle step click
  const handleStepClick = (index: number) => {
    setActiveStep(index + 1)
  }
  
  return (
    <div className={`w-full ${className}`}>
      <div className="container mx-auto px-4">
        {/* Section header */}
        <div className="flex items-center space-x-3 mb-8">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600/20">
            <svg className="h-5 w-5 text-blue-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L20 7V17L12 22L4 17V7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M20 7L12 12L4 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <h2 className="text-lg font-semibold uppercase tracking-wider text-blue-600">HOW IT WORKS</h2>
        </div>
        
        <h3 className="text-3xl font-bold text-white mb-12">Your SaaS Journey</h3>
        
        {/* Journey flow */}
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 mb-8">
          {journeySteps.map((step, index) => (
            <div 
              key={step.id}
              className={`relative ${index < journeySteps.length - 1 ? 'lg:after:content-[""] lg:after:absolute lg:after:top-1/2 lg:after:right-0 lg:after:w-full lg:after:h-[2px] lg:after:bg-gradient-to-r lg:after:from-blue-500 lg:after:to-purple-500 lg:after:transform lg:after:-translate-y-1/2 lg:after:z-0' : ''}`}
              onClick={() => handleStepClick(index)}
            >
              <div className={`
                relative z-10 p-6 rounded-lg border transition-all duration-300 cursor-pointer
                ${step.isActive ? 'bg-gradient-to-br from-blue-900/50 to-purple-900/50 border-blue-500' : 'bg-slate-900/50 border-slate-800'}
              `}>
                {/* Step indicator */}
                <div className="absolute -top-3 -right-3">
                  {step.isCompleted ? (
                    <CheckCircle2 className="h-6 w-6 text-green-500" />
                  ) : (
                    <div className={`
                      h-6 w-6 rounded-full flex items-center justify-center text-xs
                      ${step.isActive ? 'bg-blue-600 text-white' : 'bg-slate-700 text-slate-300'}
                    `}>
                      {index + 1}
                    </div>
                  )}
                </div>
                
                {/* Step content */}
                <div className="mb-4">
                  <div className={`
                    h-12 w-12 rounded-full flex items-center justify-center mb-4
                    ${step.isActive ? 'bg-blue-600/20 text-blue-400' : 'bg-slate-800 text-slate-400'}
                  `}>
                    {getStepIcon(step.id)}
                  </div>
                  <h4 className={`text-xl font-bold mb-2 ${step.isActive ? 'text-white' : 'text-slate-300'}`}>
                    {step.title}
                  </h4>
                  <p className={`text-sm ${step.isActive ? 'text-slate-200' : 'text-slate-400'}`}>
                    {step.description}
                  </p>
                </div>
                
                {/* Progress indicator for active step */}
                {step.isActive && (
                  <div className="w-full bg-slate-700 h-1 rounded-full overflow-hidden mt-4">
                    <motion.div
                      className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                      initial={{ width: "0%" }}
                      animate={{ width: "100%" }}
                      transition={{ duration: 3, repeat: Infinity }}
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {/* Navigation dots */}
        <div className="flex justify-center space-x-2 mt-8">
          {journeySteps.map((_, index) => (
            <button
              key={index}
              onClick={() => handleStepClick(index)}
              className={`h-2 w-2 rounded-full transition-all ${
                activeStep === index + 1 ? 'bg-blue-500 w-6' : 'bg-slate-600 hover:bg-slate-500'
              }`}
              aria-label={`Go to step ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

// Helper function to get the appropriate icon for each step
function getStepIcon(stepId: string) {
  switch (stepId) {
    case 'ideate':
      return (
        <svg className="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2V6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <path d="M12 18V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <path d="M4.93 4.93L7.76 7.76" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <path d="M16.24 16.24L19.07 19.07" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <path d="M2 12H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <path d="M18 12H22" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <path d="M4.93 19.07L7.76 16.24" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <path d="M16.24 7.76L19.07 4.93" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <circle cx="12" cy="12" r="4" stroke="currentColor" strokeWidth="2"/>
        </svg>
      )
    case 'analyze':
      return (
        <svg className="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 18L12 14L16 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M3 20H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <path d="M4 12H8V20H4V12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M10 8H14V20H10V8Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M16 4H20V20H16V4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    case 'design':
      return (
        <svg className="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M3 7L9 4L15 7L21 4V17L15 20L9 17L3 20V7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M9 17V4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M15 20V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    case 'plan':
      return (
        <svg className="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 11H15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M9 15H15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M3 7H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    case 'build':
      return (
        <svg className="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 9L11 12L8 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M20 4L13 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M4 4H10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M4 20H10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    case 'launch':
      return (
        <svg className="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    default:
      return null
  }
} 