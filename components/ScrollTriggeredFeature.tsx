"use client"

import React, { useEffect, useRef, useState } from 'react'
import { motion, useInView, useAnimation } from 'framer-motion'
import { ChevronDown } from 'lucide-react'

interface FeaturePoint {
  title: string
  description: string
  icon: React.ReactNode
}

interface ScrollTriggeredFeatureProps {
  title: string
  description: string
  imageSrc: string
  imageAlt: string
  featurePoints: FeaturePoint[]
  imagePosition?: 'left' | 'right'
  className?: string
}

export default function ScrollTriggeredFeature({
  title,
  description,
  imageSrc,
  imageAlt,
  featurePoints,
  imagePosition = 'left',
  className = '',
}: ScrollTriggeredFeatureProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: false, amount: 0.3 })
  const controls = useAnimation()
  const [hasTriggered, setHasTriggered] = useState(false)
  
  useEffect(() => {
    if (isInView && !hasTriggered) {
      controls.start('visible')
      setHasTriggered(true)
    } else if (!isInView && hasTriggered) {
      setHasTriggered(false)
    }
  }, [isInView, controls, hasTriggered])

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  }

  const imageVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  return (
    <div 
      ref={containerRef}
      className={`relative w-full py-16 md:py-24 ${className}`}
    >
      <div className="absolute inset-0 bg-gradient-to-b from-slate-950/50 via-blue-950/10 to-purple-950/10 z-0"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className={`grid grid-cols-1 ${imagePosition === 'left' ? 'lg:grid-cols-12' : 'lg:grid-cols-12 lg:flex-row-reverse'} gap-8 items-center`}>
          {/* Image column */}
          <div className={`${imagePosition === 'left' ? 'lg:col-span-6 lg:order-1' : 'lg:col-span-6 lg:order-2'}`}>
            <motion.div
              className="relative rounded-xl overflow-hidden shadow-2xl border border-white/10 bg-slate-900/50 backdrop-blur-sm"
              variants={imageVariants}
              initial="hidden"
              animate={controls}
            >
              <img
                src={imageSrc}
                alt={imageAlt}
                className="w-full h-auto object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-slate-900/30 to-transparent pointer-events-none"></div>
            </motion.div>
            
            {/* Scroll indicator */}
            <motion.div 
              className="hidden lg:flex justify-center mt-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: isInView ? 1 : 0 }}
              transition={{ delay: 1 }}
            >
              <div className="flex flex-col items-center text-slate-400 text-sm">
                <p>Scroll for details</p>
                <ChevronDown className="mt-2 animate-bounce" size={16} />
              </div>
            </motion.div>
          </div>
          
          {/* Content column */}
          <div className={`${imagePosition === 'left' ? 'lg:col-span-6 lg:order-2' : 'lg:col-span-6 lg:order-1'}`}>
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate={controls}
              className="space-y-8"
            >
              <motion.div variants={itemVariants}>
                <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">{title}</h2>
                <p className="text-lg text-slate-300">{description}</p>
              </motion.div>
              
              <div className="space-y-6">
                {featurePoints.map((point, index) => (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="flex items-start"
                  >
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-600/20 flex items-center justify-center mr-4">
                      {point.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">{point.title}</h3>
                      <p className="text-slate-300">{point.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 