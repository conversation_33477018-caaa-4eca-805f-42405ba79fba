"use client"

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowRight, Check, BarChart3, Code, Workflow, Kanban, Zap, Rocket } from "lucide-react"
import UserFlowDiagram from './UserFlowDiagram'

interface LiveUserFlowModelProps {
  className?: string
}

type FlowStep = {
  id: number
  title: string
  description: string
  icon: React.ReactNode
  color: string
}

export default function LiveUserFlowModel({ className = '' }: LiveUserFlowModelProps) {
  const [activeStep, setActiveStep] = useState(0)
  const [autoPlay, setAutoPlay] = useState(true)
  const [isVisible, setIsVisible] = useState(false)
  const [showDiagram, setShowDiagram] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  
  // Define the flow steps
  const flowSteps: FlowStep[] = [
    {
      id: 1,
      title: "Idea Input",
      description: "Enter your SaaS idea and business model details",
      icon: <Zap className="h-6 w-6" />,
      color: "from-blue-500 to-indigo-500"
    },
    {
      id: 2,
      title: "AI Analysis",
      description: "Our AI analyzes market viability across key metrics",
      icon: <BarChart3 className="h-6 w-6" />,
      color: "from-indigo-500 to-purple-500"
    },
    {
      id: 3,
      title: "User Flow Design",
      description: "Map out the complete user journey visually",
      icon: <Workflow className="h-6 w-6" />,
      color: "from-purple-500 to-pink-500"
    },
    {
      id: 4,
      title: "Project Planning",
      description: "Convert flows to actionable tasks and milestones",
      icon: <Kanban className="h-6 w-6" />,
      color: "from-pink-500 to-rose-500"
    },
    {
      id: 5,
      title: "Development",
      description: "Build with AI-assisted coding and integrations",
      icon: <Code className="h-6 w-6" />,
      color: "from-rose-500 to-orange-500"
    },
    {
      id: 6,
      title: "Launch",
      description: "Deploy your SaaS with analytics and growth tools",
      icon: <Rocket className="h-6 w-6" />,
      color: "from-orange-500 to-yellow-500"
    }
  ]
  
  // Intersection observer to detect when component is in view
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      { threshold: 0.3 }
    )
    
    if (containerRef.current) {
      observer.observe(containerRef.current)
    }
    
    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current)
      }
    }
  }, [])
  
  // Auto-advance through steps when visible
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (isVisible && autoPlay) {
      interval = setInterval(() => {
        setActiveStep((prev) => (prev + 1) % flowSteps.length)
      }, 3000)
    }
    
    return () => clearInterval(interval)
  }, [isVisible, autoPlay, flowSteps.length])
  
  // Handle manual navigation
  const goToStep = (index: number) => {
    setAutoPlay(false)
    setActiveStep(index)
  }
  
  // Resume autoplay after 10 seconds of inactivity
  useEffect(() => {
    const timer = setTimeout(() => {
      setAutoPlay(true)
    }, 10000)
    
    return () => clearTimeout(timer)
  }, [activeStep])
  
  return (
    <div 
      ref={containerRef}
      className={`relative w-full py-16 md:py-20 ${className}`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* View toggle */}
        <div className="flex justify-center mb-8">
          <div className="inline-flex rounded-md shadow-sm" role="group">
            <button
              type="button"
              onClick={() => setShowDiagram(false)}
              className={`px-4 py-2 text-sm font-medium rounded-l-lg ${
                !showDiagram 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-slate-800 text-slate-300 hover:bg-slate-700'
              }`}
            >
              Step-by-Step View
            </button>
            <button
              type="button"
              onClick={() => setShowDiagram(true)}
              className={`px-4 py-2 text-sm font-medium rounded-r-lg ${
                showDiagram 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-slate-800 text-slate-300 hover:bg-slate-700'
              }`}
            >
              Flow Diagram
            </button>
          </div>
        </div>
        
        {/* Flow visualization */}
        <div className="relative mb-16 overflow-hidden rounded-xl bg-slate-900/50 backdrop-blur-sm border border-white/10 p-8">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 z-0"></div>
          
          {!showDiagram ? (
            <>
              {/* Progress bar */}
              <div className="relative z-10 mb-10">
                <div className="flex items-center justify-between">
                  {flowSteps.map((step, index) => (
                    <div 
                      key={step.id} 
                      className="flex flex-col items-center"
                      onClick={() => goToStep(index)}
                    >
                      <div 
                        className={`
                          relative z-10 flex h-10 w-10 items-center justify-center rounded-full 
                          transition-all duration-300 cursor-pointer
                          ${index <= activeStep 
                            ? `bg-gradient-to-r ${step.color} text-white` 
                            : 'bg-slate-800 text-slate-400'}
                        `}
                      >
                        {index < activeStep ? (
                          <Check className="h-5 w-5" />
                        ) : (
                          <span>{index + 1}</span>
                        )}
                      </div>
                      
                      <span 
                        className={`
                          mt-2 text-xs font-medium hidden sm:block
                          ${index <= activeStep ? 'text-white' : 'text-slate-400'}
                        `}
                      >
                        {step.title}
                      </span>
                      
                      {/* Connector line */}
                      {index < flowSteps.length - 1 && (
                        <div className="absolute left-0 top-5 h-[2px] w-full -z-10">
                          <div 
                            className={`
                              h-full transition-all duration-700 ease-out
                              ${index < activeStep ? 'bg-gradient-to-r from-blue-500 to-purple-500' : 'bg-slate-800'}
                            `}
                            style={{ 
                              width: `${100 / (flowSteps.length - 1)}%`,
                              transform: `translateX(${index * 100}%)` 
                            }}
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Active step content */}
              <div className="relative z-10">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeStep}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                    className="flex flex-col md:flex-row items-center gap-8"
                  >
                    {/* Step visualization */}
                    <div className="w-full md:w-1/2">
                      <div className={`
                        aspect-video rounded-lg bg-gradient-to-br ${flowSteps[activeStep].color} 
                        p-8 flex items-center justify-center shadow-lg
                      `}>
                        <div className="text-white text-6xl">
                          {flowSteps[activeStep].icon}
                        </div>
                      </div>
                    </div>
                    
                    {/* Step description */}
                    <div className="w-full md:w-1/2 space-y-4">
                      <h3 className="text-2xl md:text-3xl font-bold text-white">
                        {flowSteps[activeStep].title}
                      </h3>
                      <p className="text-slate-300 text-lg">
                        {flowSteps[activeStep].description}
                      </p>
                      
                      {/* Navigation buttons */}
                      <div className="flex space-x-4 pt-4">
                        <button
                          onClick={() => goToStep((activeStep - 1 + flowSteps.length) % flowSteps.length)}
                          className="px-4 py-2 rounded-lg bg-slate-800 text-white hover:bg-slate-700 transition"
                        >
                          Previous
                        </button>
                        <button
                          onClick={() => goToStep((activeStep + 1) % flowSteps.length)}
                          className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-500 transition flex items-center gap-2"
                        >
                          Next <ArrowRight className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                </AnimatePresence>
              </div>
            </>
          ) : (
            <div className="relative z-10">
              <UserFlowDiagram activeStep={activeStep + 1} className="h-[600px] mx-auto" />
              
              {/* Step navigation for diagram view */}
              <div className="flex justify-center mt-8 space-x-4">
                <button
                  onClick={() => goToStep((activeStep - 1 + flowSteps.length) % flowSteps.length)}
                  className="px-4 py-2 rounded-lg bg-slate-800 text-white hover:bg-slate-700 transition"
                >
                  Previous Step
                </button>
                <button
                  onClick={() => goToStep((activeStep + 1) % flowSteps.length)}
                  className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-500 transition flex items-center gap-2"
                >
                  Next Step <ArrowRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}
          
          {/* Auto-play indicator */}
          <div className="absolute bottom-4 right-4 flex items-center gap-2">
            <div 
              className={`h-2 w-2 rounded-full ${autoPlay ? 'bg-green-500 animate-pulse' : 'bg-slate-500'}`}
            />
            <span className="text-xs text-slate-400">
              {autoPlay ? 'Auto-advancing' : 'Manual navigation'}
            </span>
          </div>
        </div>
        
        {/* Interactive demo CTA */}
        <div className="text-center space-y-4">
          <button
            onClick={() => setAutoPlay(!autoPlay)}
            className="px-6 py-3 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 transition"
          >
            {autoPlay ? 'Pause Auto-Play' : 'Resume Auto-Play'}
          </button>
          
          <p className="text-slate-400 text-sm">
            Toggle between views and navigate through steps to see how SaaSifyX works
          </p>
        </div>
      </div>
    </div>
  )
} 