import { Check } from "lucide-react";

interface PricingCardProps {
  name: string;
  price: number;
  features: string[];
  isRecommended?: boolean;
}

export default function PricingCard({ name, price, features, isRecommended = false }: PricingCardProps) {
  // Generate a random card number-like string for visual effect
  const cardNumber = Array(4).fill(0).map(() => Math.random().toString(36).substring(2, 6).toUpperCase()).join(' ');
  
  // Generate expiry date (always show as valid for next 3 years from current date)
  const today = new Date();
  const expiryMonth = (today.getMonth() + 1).toString().padStart(2, '0');
  const expiryYear = (today.getFullYear() + 3).toString().slice(-2);

  // Determine cardholder name based on plan
  const getCardholderName = () => {
    switch(name.toLowerCase()) {
      case 'starter':
        return 'KAVI';
      case 'pro':
        return 'SHASHANK';
      case 'enterprise':
        return 'SANTHOSH';
      default:
        return 'KAVI';
    }
  };

  return (
    <div className={`pricing-card ${isRecommended ? 'pricing-card-featured' : 'pricing-card-regular'}`}>
      {isRecommended && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
          Most Popular
        </div>
      )}
      
      <div className="pricing-card-header">
        <div>
          <h3 className="text-xl font-bold text-white">{name}</h3>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold text-white">
            {price === 0 ? 'Free' : `$${price}`}
          </div>
          {price > 0 && <div className="text-sm text-slate-400">/month</div>}
        </div>
      </div>

      <div className="pricing-card-chip-row">
        <div className="card-chip" />
        <div className="text-sm font-mono text-slate-400">
          Saasifyx
        </div>
      </div>

      <div className="pricing-card-number">
        {cardNumber}
      </div>

      <div className="pricing-card-details">
        <ul className="space-y-3 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-2 text-slate-300">
              <Check className="h-5 w-5 text-emerald-500 flex-shrink-0" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>

        <div className="pricing-card-footer">
          <div>
            <div className="text-xs text-slate-400 mb-1">VALID THRU</div>
            <div>{expiryMonth}/{expiryYear}</div>
          </div>
          <div>
            <div className="text-xs text-slate-400 mb-1">MEMBER</div>
            <div>{getCardholderName()}</div>
          </div>
        </div>
      </div>

      <button className={`mt-6 w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
        isRecommended 
          ? 'bg-blue-500 hover:bg-blue-600 text-white' 
          : 'bg-slate-700 hover:bg-slate-600 text-white'
      }`}>
        {price === 0 ? 'Get Started' : 'Start Pro'}
      </button>
    </div>
  );
} 