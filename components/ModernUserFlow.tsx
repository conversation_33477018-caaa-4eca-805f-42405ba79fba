"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Check, ChevronRight, BarChart2, Figma, ListTodo, Code, Rocket, LightbulbIcon } from 'lucide-react'

interface ModernUserFlowProps {
  className?: string
}

interface JourneyStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  status: 'completed' | 'active' | 'pending'
  color: string
}

export default function ModernUserFlow({ className = '' }: ModernUserFlowProps) {
  const [activeStepIndex, setActiveStepIndex] = useState(1)
  const [isAutoAdvancing, setIsAutoAdvancing] = useState(true)
  
  // Define journey steps
  const journeySteps: JourneyStep[] = [
    {
      id: 'ideate',
      title: 'Ideate',
      description: 'Start with your SaaS idea and let our AI analyze its potential',
      icon: <LightbulbIcon className="h-6 w-6" />,
      status: activeStepIndex > 0 ? 'completed' : 'pending',
      color: 'from-amber-500 to-orange-500'
    },
    {
      id: 'analyze',
      title: 'Analyze',
      description: 'Get comprehensive AI-powered market analysis and feasibility scores',
      icon: <BarChart2 className="h-6 w-6" />,
      status: activeStepIndex === 1 ? 'active' : activeStepIndex > 1 ? 'completed' : 'pending',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'design',
      title: 'Design',
      description: 'Create user flows and wireframes with our visual design tools',
      icon: <Figma className="h-6 w-6" />,
      status: activeStepIndex === 2 ? 'active' : activeStepIndex > 2 ? 'completed' : 'pending',
      color: 'from-indigo-500 to-purple-500'
    },
    {
      id: 'plan',
      title: 'Plan',
      description: 'Organize development with AI-generated tasks and smart Kanban boards',
      icon: <ListTodo className="h-6 w-6" />,
      status: activeStepIndex === 3 ? 'active' : activeStepIndex > 3 ? 'completed' : 'pending',
      color: 'from-violet-500 to-purple-500'
    },
    {
      id: 'build',
      title: 'Build',
      description: 'Code efficiently with Cursor AI integration and premium development tools',
      icon: <Code className="h-6 w-6" />,
      status: activeStepIndex === 4 ? 'active' : activeStepIndex > 4 ? 'completed' : 'pending',
      color: 'from-fuchsia-500 to-pink-500'
    },
    {
      id: 'launch',
      title: 'Launch',
      description: 'Deploy your SaaS with confidence using our launch optimization features',
      icon: <Rocket className="h-6 w-6" />,
      status: activeStepIndex === 5 ? 'active' : 'pending',
      color: 'from-green-500 to-emerald-500'
    }
  ]
  
  // Auto advance through steps
  useEffect(() => {
    if (!isAutoAdvancing) return
    
    const interval = setInterval(() => {
      setActiveStepIndex(prev => (prev + 1) % journeySteps.length)
    }, 3000)
    
    return () => clearInterval(interval)
  }, [isAutoAdvancing, journeySteps.length])
  
  // Handle step click
  const handleStepClick = (index: number) => {
    setActiveStepIndex(index)
    setIsAutoAdvancing(false)
    
    // Resume auto-advancing after 10 seconds of inactivity
    const timer = setTimeout(() => {
      setIsAutoAdvancing(true)
    }, 10000)
    
    return () => clearTimeout(timer)
  }
  
  return (
    <div className={`w-full ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600/20">
            <svg className="h-5 w-5 text-blue-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L20 7V17L12 22L4 17V7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M20 7L12 12L4 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <h2 className="text-lg font-semibold uppercase tracking-wider text-blue-600">HOW IT WORKS</h2>
        </div>
        
        <h3 className="text-3xl font-bold text-white mb-8">Your SaaS Journey</h3>
        
        {/* Top banner */}
        <div className="relative w-full mb-12">
          <div className="rounded-lg bg-gradient-to-r from-blue-900/30 to-purple-900/30 border border-blue-800/30 p-4 sm:p-6">
            <div className="flex items-center">
              <Rocket className="h-6 w-6 text-blue-400 mr-4" />
              <p className="text-blue-100 text-sm sm:text-base">
                Validate and build your SaaS ideas efficiently
              </p>
            </div>
          </div>
        </div>
        
        {/* Journey Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {journeySteps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`
                relative rounded-lg border p-6 cursor-pointer
                ${step.status === 'active' 
                  ? 'bg-gradient-to-br from-slate-900/90 to-slate-800/90 border-blue-500 shadow-lg shadow-blue-500/20' 
                  : step.status === 'completed'
                    ? 'bg-gradient-to-br from-slate-900/70 to-slate-800/70 border-green-500/50'
                    : 'bg-gradient-to-br from-slate-900/50 to-slate-800/50 border-slate-700/50'
                }
              `}
              onClick={() => handleStepClick(index)}
            >
              {/* Status indicator */}
              <div className="absolute -top-3 -right-3">
                {step.status === 'completed' ? (
                  <div className="h-6 w-6 rounded-full bg-green-500 flex items-center justify-center">
                    <Check className="h-4 w-4 text-white" />
                  </div>
                ) : step.status === 'active' ? (
                  <div className="h-6 w-6 rounded-full bg-blue-500 flex items-center justify-center">
                    <motion.div 
                      className="h-3 w-3 rounded-full bg-white"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </div>
                ) : (
                  <div className="h-6 w-6 rounded-full bg-slate-700 flex items-center justify-center">
                    <span className="text-xs text-white font-medium">{index + 1}</span>
                  </div>
                )}
              </div>
              
              {/* Icon */}
              <div className={`
                h-12 w-12 rounded-full mb-4 flex items-center justify-center
                ${step.status === 'active' 
                  ? `bg-gradient-to-br ${step.color} text-white` 
                  : step.status === 'completed'
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-slate-800 text-slate-400'
                }
              `}>
                {step.icon}
              </div>
              
              {/* Content */}
              <h4 className={`text-xl font-bold mb-2 ${
                step.status === 'pending' ? 'text-slate-400' : 'text-white'
              }`}>
                {step.title}
              </h4>
              
              <p className={`text-sm ${
                step.status === 'pending' ? 'text-slate-500' : 'text-slate-300'
              }`}>
                {step.description}
              </p>
              
              {/* Progress indicator for active step */}
              {step.status === 'active' && (
                <div className="w-full bg-slate-700 h-1 rounded-full overflow-hidden mt-4">
                  <motion.div
                    className={`h-full bg-gradient-to-r ${step.color}`}
                    initial={{ width: "0%" }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 3, repeat: Infinity }}
                  />
                </div>
              )}
              
              {/* Connection line */}
              {index < journeySteps.length - 1 && (
                <div className="hidden md:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                  <ChevronRight className={`h-6 w-6 ${
                    step.status === 'completed' ? 'text-green-500' : 'text-slate-600'
                  }`} />
                </div>
              )}
            </motion.div>
          ))}
        </div>
        
        {/* Market Analysis Preview */}
        <div className="bg-slate-900/80 backdrop-blur-sm border border-slate-800/80 rounded-lg overflow-hidden shadow-xl mb-12">
          <div className="p-6">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h3 className="text-2xl font-bold text-white">Market Feasibility</h3>
                <p className="text-slate-400">AI Analysis Preview</p>
              </div>
              <div className="text-4xl font-bold text-blue-500">
                8.2<span className="text-lg text-slate-400">/10</span>
              </div>
            </div>
            
            <div className="space-y-6">
              {[
                { name: 'Uniqueness', description: 'Market differentiation', value: 85, color: 'from-green-500 to-emerald-500' },
                { name: 'Stickiness', description: 'User retention potential', value: 92, color: 'from-blue-500 to-cyan-500' },
                { name: 'Growth', description: 'Scalability factor', value: 78, color: 'from-purple-500 to-violet-500' },
                { name: 'Pricing', description: 'Revenue optimization', value: 88, color: 'from-emerald-500 to-teal-500' }
              ].map((metric) => (
                <div key={metric.name} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="text-white font-medium">{metric.name}</h4>
                      <p className="text-xs text-slate-400">{metric.description}</p>
                    </div>
                    <span className="text-white font-bold">{metric.value}%</span>
                  </div>
                  <div className="h-2 w-full bg-slate-800 rounded-full overflow-hidden">
                    <motion.div 
                      className={`h-full bg-gradient-to-r ${metric.color}`}
                      initial={{ width: 0 }}
                      animate={{ width: `${metric.value}%` }}
                      transition={{ duration: 1, delay: 0.3 }}
                    />
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-8 text-center">
              <button className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                Analyze My Idea
              </button>
            </div>
          </div>
        </div>
        
        {/* Auto-play indicator */}
        <div className="flex items-center justify-center gap-2 text-sm text-slate-400">
          <div className={`h-2 w-2 rounded-full ${isAutoAdvancing ? 'bg-green-500 animate-pulse' : 'bg-slate-600'}`} />
          <span>{isAutoAdvancing ? 'Auto-advancing demo' : 'Click any step to explore'}</span>
          <button 
            onClick={() => setIsAutoAdvancing(!isAutoAdvancing)}
            className="ml-2 text-blue-400 hover:text-blue-300"
          >
            {isAutoAdvancing ? 'Pause' : 'Resume'}
          </button>
        </div>
      </div>
    </div>
  )
} 