"use client"

import React from 'react'
import ScrollTriggeredFeature from './ScrollTriggeredFeature'
import { BarChart3, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, ArrowUpRight, Users } from 'lucide-react'

export default function DashboardFeatureSection() {
  return (
    <ScrollTriggeredFeature
      title="Comprehensive Dashboard"
      description="Track your SaaS projects, completion rates, and time saved with our intuitive dashboard that provides real-time insights and analytics."
      imageSrc="/images/demo/dashboard.png"
      imageAlt="SaaSifyX dashboard interface showing project metrics and analytics"
      imagePosition="right"
      featurePoints={[
        {
          title: "Project Analytics",
          description: "Monitor all your SaaS projects in one place with real-time metrics and progress tracking.",
          icon: <BarChart3 className="text-blue-500" size={20} />
        },
        {
          title: "Completion Rates",
          description: "Track your project completion rates and identify bottlenecks in your development process.",
          icon: <PieChart className="text-green-500" size={20} />
        },
        {
          title: "Time Optimization",
          description: "See how much time you're saving with AI-powered automation and project management tools.",
          icon: <Clock className="text-purple-500" size={20} />
        },
        {
          title: "Performance Trends",
          description: "Analyze long-term performance trends with interactive charts and data visualization.",
          icon: <LineChart className="text-cyan-500" size={20} />
        },
        {
          title: "Team Collaboration",
          description: "Facilitate seamless team collaboration with shared dashboards and progress reports.",
          icon: <Users className="text-amber-500" size={20} />
        },
        {
          title: "Growth Metrics",
          description: "Track key growth metrics like user acquisition, retention, and revenue projections.",
          icon: <ArrowUpRight className="text-rose-500" size={20} />
        }
      ]}
    />
  )
} 