"use client"

import React, { useState } from 'react'
import { CheckCircle2, LightbulbIcon, BarChart2, Figma, ListTodo, Code, Rocket } from 'lucide-react'

interface UserJourneyStepsProps {
  className?: string
}

type JourneyStep = {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  isCompleted: boolean
}

export default function UserJourneySteps({ className = '' }: UserJourneyStepsProps) {
  const [activeStep, setActiveStep] = useState(1)
  
  const journeySteps: JourneyStep[] = [
    {
      id: 'ideate',
      title: 'Ideate',
      description: 'Start with your SaaS idea and let our AI analyze its potential',
      icon: <LightbulbIcon className="h-6 w-6" />,
      isCompleted: activeStep > 1
    },
    {
      id: 'analyze',
      title: 'Analyze',
      description: 'Get comprehensive AI-powered market analysis and feasibility scores',
      icon: <BarChart2 className="h-6 w-6" />,
      isCompleted: activeStep > 2
    },
    {
      id: 'design',
      title: 'Design',
      description: 'Create user flows and wireframes with our visual design tools',
      icon: <Figma className="h-6 w-6" />,
      isCompleted: false
    },
    {
      id: 'plan',
      title: 'Plan',
      description: 'Organize development with AI-generated tasks and smart Kanban boards',
      icon: <ListTodo className="h-6 w-6" />,
      isCompleted: false
    },
    {
      id: 'build',
      title: 'Build',
      description: 'Code efficiently with Cursor AI integration and premium development tools',
      icon: <Code className="h-6 w-6" />,
      isCompleted: false
    },
    {
      id: 'launch',
      title: 'Launch',
      description: 'Deploy your SaaS with confidence using our launch optimization features',
      icon: <Rocket className="h-6 w-6" />,
      isCompleted: false
    }
  ]
  
  return (
    <div className={`${className}`}>
      <div className="space-y-4">
        {journeySteps.map((step, index) => (
          <div 
            key={step.id}
            className={`flex items-start p-4 rounded-lg border transition-all ${
              index + 1 === activeStep 
                ? 'bg-slate-800/70 border-blue-500'
                : 'bg-slate-900/40 border-slate-800 hover:border-slate-700'
            }`}
            onClick={() => setActiveStep(index + 1)}
          >
            <div className={`
              flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center mr-4
              ${step.isCompleted 
                ? 'bg-green-500/20 text-green-500' 
                : index + 1 === activeStep 
                  ? 'bg-blue-500/20 text-blue-400'
                  : 'bg-slate-800 text-slate-400'
              }
            `}>
              {step.isCompleted ? (
                <CheckCircle2 className="h-5 w-5" />
              ) : (
                step.icon
              )}
            </div>
            
            <div>
              <div className="flex items-center">
                <h4 className={`font-semibold ${
                  index + 1 === activeStep ? 'text-white' : 'text-slate-300'
                }`}>
                  {step.title}
                </h4>
                
                {step.isCompleted && (
                  <span className="ml-2 text-xs font-medium bg-green-500/20 text-green-500 px-2 py-0.5 rounded-full">
                    Completed
                  </span>
                )}
                
                {index + 1 === activeStep && !step.isCompleted && (
                  <span className="ml-2 text-xs font-medium bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded-full">
                    Active
                  </span>
                )}
              </div>
              
              <p className={`text-sm mt-1 ${
                index + 1 === activeStep ? 'text-slate-300' : 'text-slate-400'
              }`}>
                {step.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 