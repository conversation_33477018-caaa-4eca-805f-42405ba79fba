import { useEffect, useRef, useState } from 'react';
import { motion, useAnimation } from 'framer-motion';
import Image from 'next/image';

interface ContentSection {
  id: string;
  title: string;
  description: string;
  image: string;
}

interface ScrollBasedRevealProps {
  sections: ContentSection[];
}

export default function ScrollBasedReveal({ sections }: ScrollBasedRevealProps) {
  const [activeSection, setActiveSection] = useState(0);
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const imageControls = useAnimation();

  useEffect(() => {
    // Initialize Intersection Observer for each text section
    const observers = sectionRefs.current.map((ref, index) => {
      if (!ref) return null;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setActiveSection(index);
            // Animate the image change
            imageControls.start({
              opacity: 1,
              x: 0,
              transition: { duration: 0.5 }
            });
          }
        },
        {
          threshold: 0.5, // Trigger when 50% of the section is visible
          rootMargin: '-20% 0px -20% 0px' // Add some margin to make the trigger more precise
        }
      );

      observer.observe(ref);
      return observer;
    });

    // Cleanup observers on unmount
    return () => {
      observers.forEach(observer => observer?.disconnect());
    };
  }, [imageControls]);

  return (
    <div className="flex flex-col md:flex-row gap-8 min-h-screen">
      {/* Text sections on the left */}
      <div className="md:w-1/2 space-y-24 py-12">
        {sections.map((section, index) => (
          <div
            key={section.id}
            ref={el => (sectionRefs.current[index] = el)}
            className={`p-6 rounded-lg transition-all duration-300 ${
              activeSection === index
                ? 'bg-gray-100 dark:bg-gray-800 shadow-lg scale-105'
                : 'opacity-50'
            }`}
          >
            <h3 className="text-2xl font-bold mb-4">{section.title}</h3>
            <p className="text-gray-600 dark:text-gray-300">
              {section.description}
            </p>
          </div>
        ))}
      </div>

      {/* Image container on the right */}
      <div className="md:w-1/2 sticky top-0 h-screen flex items-center">
        <motion.div
          animate={imageControls}
          initial={{ opacity: 0, x: 50 }}
          className="w-full h-[600px] relative rounded-lg overflow-hidden"
        >
          <Image
            src={sections[activeSection].image}
            alt={sections[activeSection].title}
            fill
            className="object-cover"
            priority
          />
        </motion.div>
      </div>
    </div>
  );
} 