'use client';

import { motion } from 'framer-motion';
import React from 'react';

const features = [
  {
    title: "Cursor AI Integration",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
      </svg>
    ),
    description: "Advanced coding assistance integrated directly into your workflow",
    color: "from-amber-600/20 via-amber-500/10 to-transparent",
    iconColor: "text-amber-400"
  }
];

const processSteps = ['Ideation', 'Analysis', 'Design', 'Development', 'Launch'];

const AnimatedFeatures = () => {
  return (
    <section className="relative py-24 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-slate-950/50 via-blue-950/20 to-purple-950/20 z-0"></div>
      
      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Transform Your Ideas Into Reality
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Our powerful suite of tools guides you through every step of your SaaS journey
            </p>
          </motion.div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 gap-6 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative group"
            >
              {/* Glow Effect */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl blur-xl`}></div>
              
              {/* Card */}
              <div className="relative bg-slate-900/90 backdrop-blur-sm border border-slate-800/50 rounded-2xl p-6 h-full overflow-hidden">
                {/* Top Icons */}
                <div className="flex items-start justify-between mb-6">
                  <div className={`w-10 h-10 rounded-lg ${feature.iconColor} bg-slate-800/50 flex items-center justify-center`}>
                    {feature.icon}
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                <p className="text-slate-400 text-sm leading-relaxed">{feature.description}</p>

                {/* Hover Effect Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-slate-900/90 pointer-events-none"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Process Flow */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 1 }}
          viewport={{ once: true }}
          className="relative mt-20"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl"></div>
          <div className="relative p-8 rounded-3xl backdrop-blur-sm border border-slate-700/50">
            <h3 className="text-2xl font-bold text-white mb-8 text-center">Watch SaaSifyX in Action</h3>
            
            <div className="flex justify-center">
              <div className="relative w-full max-w-4xl">
                <motion.div
                  animate={{
                    scale: [1, 1.02, 1],
                    transition: {
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }
                  }}
                  className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur-xl"
                ></motion.div>
                
                <div className="relative bg-slate-900/50 rounded-xl p-6">
                  <div className="flex items-center justify-between space-x-4">
                    {processSteps.map((step, index) => (
                      <motion.div
                        key={step}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.2 }}
                        className="flex flex-col items-center relative"
                      >
                        <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mb-2">
                          <span className="text-blue-400 font-bold">{index + 1}</span>
                        </div>
                        <span className="text-white font-medium">{step}</span>
                        {index < processSteps.length - 1 && (
                          <motion.div
                            initial={{ scaleX: 0 }}
                            animate={{ scaleX: 1 }}
                            transition={{ delay: index * 0.2, duration: 0.5 }}
                            className="absolute w-[calc(100%-3rem)] h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"
                            style={{
                              left: '100%',
                              top: '1.5rem',
                              transformOrigin: 'left'
                            }}
                          />
                        )}
                      </motion.div>
                    ))}
                  </div>
                  
                  {/* Progress Indicator */}
                  <motion.div
                    animate={{
                      x: ['0%', '100%'],
                      opacity: [0, 1, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="h-1 bg-blue-500/50 rounded-full mt-8"
                  ></motion.div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AnimatedFeatures; 