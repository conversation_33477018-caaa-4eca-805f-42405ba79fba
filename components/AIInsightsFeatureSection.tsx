"use client"

import React from 'react'
import ScrollTriggeredFeature from './ScrollTriggeredFeature'
import { Brain, Zap, Target, TrendingUp, Sparkles, Layers } from 'lucide-react'

export default function AIInsightsFeatureSection() {
  return (
    <ScrollTriggeredFeature
      title="AI-Powered Insights"
      description="Leverage advanced AI analytics to gain valuable insights about your SaaS projects and make data-driven decisions for optimal results."
      imageSrc="/images/demo/ai-insights.png"
      imageAlt="SaaSifyX AI insights dashboard showing analytics and recommendations"
      imagePosition="left"
      featurePoints={[
        {
          title: "Market Analysis",
          description: "Get comprehensive market analysis to understand your competitive landscape and target audience.",
          icon: <Target className="text-emerald-500" size={20} />
        },
        {
          title: "Predictive Analytics",
          description: "Utilize AI-powered predictive analytics to forecast future trends and potential challenges.",
          icon: <TrendingUp className="text-blue-500" size={20} />
        },
        {
          title: "Intelligent Recommendations",
          description: "Receive personalized recommendations to optimize your SaaS product and business strategy.",
          icon: <Sparkles className="text-amber-500" size={20} />
        },
        {
          title: "Neural Processing",
          description: "Our advanced neural networks analyze complex patterns in your data to extract meaningful insights.",
          icon: <Brain className="text-purple-500" size={20} />
        },
        {
          title: "Real-time Processing",
          description: "Get instant insights with our real-time data processing and analysis capabilities.",
          icon: <Zap className="text-rose-500" size={20} />
        },
        {
          title: "Multi-dimensional Analysis",
          description: "Explore your data from multiple perspectives with our comprehensive analytical framework.",
          icon: <Layers className="text-cyan-500" size={20} />
        }
      ]}
    />
  )
} 