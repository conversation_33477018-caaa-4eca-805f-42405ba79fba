"use client"

import CountUp from "@/components/animations/count-up"
import <PERSON>rollFloat from "@/components/animations/scroll-float"
import { motion, useMotionValue, useTransform, AnimatePresence } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef, useState } from 'react'
import { BarChart3, Zap, Users, Clock } from 'lucide-react'
import { Button } from "@/components/ui/button"

export default function Statistics() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);

  // Floating animation for the badge
  const y = useMotionValue(0);
  const rotate = useTransform(y, [-10, 10], [-5, 5]);

  const getIcon = (index: number) => {
    switch(index) {
      case 0: return <BarChart3 className="w-6 h-6" />;
      case 1: return <Zap className="w-6 h-6" />;
      case 2: return <Users className="w-6 h-6" />;
      case 3: return <Clock className="w-6 h-6" />;
      default: return null;
    }
  };

  return (
    <section ref={sectionRef} className="relative py-32 lg:py-48 overflow-hidden">
      {/* Enhanced background effects */}
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: isInView ? 1 : 0 }}
        transition={{ duration: 1 }}
        className="absolute inset-0"
      >
        {/* Circuit board pattern */}
        <div className="absolute inset-0 opacity-[0.03] bg-[url('/patterns/circuit-board.svg')] bg-repeat" />
        
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950" />
        
        {/* Radial glow */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.1)_0%,transparent_50%)]" />
        
        {/* Binary code pattern */}
        <div className="absolute inset-0 opacity-[0.02] mix-blend-overlay">
          {[...Array(20)].map((_, i) => (
            <div 
              key={i}
              className="absolute text-xs font-mono text-blue-500"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                transform: `rotate(${Math.random() * 360}deg)`
              }}
            >
              {Math.random().toString(2).slice(2, 10)}
            </div>
          ))}
        </div>

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(30)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-blue-500/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: Math.random() * 2 + 2,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
      </motion.div>

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="flex flex-col items-center justify-center space-y-16 text-center">
          <ScrollFloat direction="up" className="space-y-8 max-w-4xl">
            {/* Animated badge */}
            <motion.div
              style={{ y, rotate }}
              whileHover={{ scale: 1.05 }}
              drag="y"
              dragConstraints={{ top: -10, bottom: 10 }}
              className="inline-flex items-center rounded-full bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-xl border border-blue-500/30 px-6 py-3 text-sm text-blue-400 cursor-pointer"
            >
              <span className="mr-2">🚀</span>
              Proven Results
            </motion.div>

            {/* Animated heading */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold tracking-tight leading-none"
              style={{ fontFamily: 'var(--font-gilroy)' }}
            >
              Trusted by developers{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 animate-gradient">
                worldwide
              </span>
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mx-auto max-w-3xl text-xl md:text-2xl text-slate-400 leading-relaxed font-light"
              style={{ fontFamily: 'var(--font-inter)' }}
            >
              Join thousands of successful developers who are building profitable SaaS applications with our premium
              platform.
            </motion.p>
          </ScrollFloat>

          {/* Stats Grid with Asymmetrical Layout */}
          <div className="w-full mt-16">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
              {statistics.map((stat, index) => (
                <ScrollFloat 
                  key={stat.label} 
                  direction="up" 
                  delay={0.05 * (index + 1)} 
                  className="w-full"
                >
                  <motion.div
                    className={`stat-card group relative overflow-hidden transform ${
                      index % 2 === 0 ? 'lg:-mt-8' : ''
                    }`}
                    onHoverStart={() => setHoveredCard(index)}
                    onHoverEnd={() => setHoveredCard(null)}
                    whileHover={{ scale: 1.02, y: -5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    {/* Glass morphism card */}
                    <div className="relative bg-slate-900/40 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-8 h-full">
                      {/* Glow effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-blue-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl blur-xl"
                        animate={{
                          scale: hoveredCard === index ? [1, 1.2, 1] : 1,
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut",
                        }}
                      />

                      <div className="relative flex flex-col items-center justify-center text-center space-y-4">
                        {/* Icon */}
                        <motion.div
                          className={`p-3 rounded-xl bg-gradient-to-br from-blue-600/20 to-purple-600/20 text-blue-400 group-hover:text-blue-300 transition-colors`}
                          animate={{
                            rotate: hoveredCard === index ? 360 : 0,
                          }}
                          transition={{ duration: 2, ease: "easeInOut" }}
                        >
                          {getIcon(index)}
                        </motion.div>

                        {/* Value */}
                        <motion.div
                          className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400"
                          initial={{ opacity: 0, scale: 0.5 }}
                          animate={isInView ? { opacity: 1, scale: 1 } : {}}
                          transition={{ duration: 0.5, delay: 0.1 * index }}
                        >
                          <CountUp from={0} to={stat.value} separator="," duration={2} />
                          <span>{stat.suffix}</span>
                        </motion.div>

                        {/* Label */}
                        <p className="text-base sm:text-lg text-slate-300 font-medium">{stat.label}</p>

                        {/* Underline */}
                        <motion.div
                          className="h-0.5 w-12 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 rounded-full"
                          initial={{ width: 0 }}
                          animate={isInView ? { width: "3rem" } : {}}
                          transition={{ duration: 0.8, delay: 0.2 * index }}
                        />
                      </div>
                    </div>
                  </motion.div>
                </ScrollFloat>
              ))}
            </div>
          </div>

          {/* CTA Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mt-12"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-6 text-lg rounded-xl transform transition-all duration-300 hover:scale-105 hover:shadow-[0_0_20px_rgba(59,130,246,0.5)]"
            >
              Join Now
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

const statistics = [
  {
    value: 5000,
    suffix: "+",
    label: "Projects Analyzed",
  },
  {
    value: 87,
    suffix: "%",
    label: "Success Rate",
  },
  {
    value: 3200,
    suffix: "+",
    label: "Active Users",
  },
  {
    value: 42,
    suffix: "%",
    label: "Time Saved",
  },
]
