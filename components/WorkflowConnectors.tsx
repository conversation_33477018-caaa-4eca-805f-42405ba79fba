import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface WorkflowConnectorProps {
  startPoint: { x: number; y: number };
  endPoint: { x: number; y: number };
  color: string;
  glowColor: string;
  delay?: number;
  curveType?: 'up' | 'down' | 'left' | 'right';
}

const WorkflowConnector = ({ startPoint, endPoint, color, glowColor, delay = 0, curveType = 'right' }: WorkflowConnectorProps) => {
  const [pathLength, setPathLength] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);

  const generatePath = () => {
    const offset = 60; // Consistent offset for all curves
    let path = '';

    switch (curveType) {
      case 'right':
        const midX = startPoint.x + (endPoint.x - startPoint.x) / 2;
        path = `M ${startPoint.x} ${startPoint.y} 
                C ${startPoint.x + offset} ${startPoint.y},
                  ${midX - offset} ${endPoint.y},
                  ${endPoint.x} ${endPoint.y}`;
        break;
      case 'down':
        const midY = startPoint.y + (endPoint.y - startPoint.y) / 2;
        path = `M ${startPoint.x} ${startPoint.y}
                C ${startPoint.x} ${startPoint.y + offset},
                  ${endPoint.x} ${endPoint.y - offset},
                  ${endPoint.x} ${endPoint.y}`;
        break;
      case 'left':
        const midXLeft = startPoint.x - (startPoint.x - endPoint.x) / 2;
        path = `M ${startPoint.x} ${startPoint.y}
                C ${startPoint.x - offset} ${startPoint.y},
                  ${midXLeft + offset} ${endPoint.y},
                  ${endPoint.x} ${endPoint.y}`;
        break;
      case 'up':
        const midYUp = startPoint.y - (startPoint.y - endPoint.y) / 2;
        path = `M ${startPoint.x} ${startPoint.y}
                C ${startPoint.x} ${startPoint.y - offset},
                  ${endPoint.x} ${endPoint.y + offset},
                  ${endPoint.x} ${endPoint.y}`;
        break;
    }
    return path;
  };

  return (
    <g>
      {/* Glow effect */}
      <path
        d={generatePath()}
        stroke={glowColor}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
        opacity="0.2"
        filter="url(#glow)"
      />
      
      {/* Main path */}
      <motion.path
        d={generatePath()}
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{ pathLength: isVisible ? 1 : 0, opacity: isVisible ? 1 : 0 }}
        transition={{ duration: 1, ease: "easeInOut" }}
      />

      {/* Animated particle */}
      {isVisible && (
        <motion.circle
          r="4"
          fill={color}
          filter="url(#glow)"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <animateMotion
            dur="3s"
            repeatCount="indefinite"
            path={generatePath()}
          />
        </motion.circle>
      )}
    </g>
  );
};

export default function WorkflowConnectors() {
  // Theme colors for each feature
  const colors = {
    aiAnalysis: "#3B82F6", // Blue
    userFlow: "#8B5CF6",   // Purple
    smartKanban: "#10B981", // Green
    cursorAI: "#F59E0B"    // Amber
  };
  
  // Card dimensions and spacing
  const cardWidth = 380;  // Slightly reduced width
  const cardHeight = 120; // Slightly reduced height
  const gridGap = 160;    // Consistent gap for both horizontal and vertical
  
  // Base positioning values
  const baseX = 100;  // Left margin
  const baseY = 80;   // Top margin
  
  // Calculate positions in a proper 2x2 grid layout
  const cardPositions = {
    // Top row
    aiAnalysis: {
      x: baseX,
      y: baseY
    },
    userFlow: {
      x: baseX + cardWidth + gridGap,
      y: baseY
    },
    // Bottom row
    cursorAI: {
      x: baseX,
      y: baseY + cardHeight + gridGap
    },
    smartKanban: {
      x: baseX + cardWidth + gridGap,
      y: baseY + cardHeight + gridGap
    }
  };

  // Helper function to calculate connection points
  const getConnectionPoint = (card: keyof typeof cardPositions, position: 'right' | 'left' | 'top' | 'bottom' | 'center') => {
    const pos = cardPositions[card];
    switch (position) {
      case 'right':
        return { x: pos.x + cardWidth, y: pos.y + cardHeight / 2 };
      case 'left':
        return { x: pos.x, y: pos.y + cardHeight / 2 };
      case 'top':
        return { x: pos.x + cardWidth / 2, y: pos.y };
      case 'bottom':
        return { x: pos.x + cardWidth / 2, y: pos.y + cardHeight };
      case 'center':
        return { x: pos.x + cardWidth / 2, y: pos.y + cardHeight / 2 };
    }
  };

  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg className="absolute inset-0 w-full h-full">
        <defs>
          {Object.entries(colors).map(([key, color]) => (
            <linearGradient key={key} id={`${key}Gradient`} gradientUnits="userSpaceOnUse">
              <stop offset="0%" stopColor={color} stopOpacity="0.2" />
              <stop offset="50%" stopColor={color} stopOpacity="0.5" />
              <stop offset="100%" stopColor={color} stopOpacity="0.2" />
            </linearGradient>
          ))}
          
          {/* Enhanced glow effect */}
          <filter id="glow">
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* AI Analysis to User Flow - Horizontal connection */}
        <WorkflowConnector
          startPoint={getConnectionPoint('aiAnalysis', 'right')}
          endPoint={getConnectionPoint('userFlow', 'left')}
          color={colors.aiAnalysis}
          glowColor={colors.aiAnalysis}
          delay={0}
          curveType="right"
        />

        {/* User Flow to Smart Kanban - Vertical connection */}
        <WorkflowConnector
          startPoint={getConnectionPoint('userFlow', 'bottom')}
          endPoint={getConnectionPoint('smartKanban', 'top')}
          color={colors.userFlow}
          glowColor={colors.userFlow}
          delay={300}
          curveType="down"
        />

        {/* Smart Kanban to Cursor AI - Horizontal connection */}
        <WorkflowConnector
          startPoint={getConnectionPoint('smartKanban', 'left')}
          endPoint={getConnectionPoint('cursorAI', 'right')}
          color={colors.smartKanban}
          glowColor={colors.smartKanban}
          delay={600}
          curveType="left"
        />

        {/* Cursor AI to AI Analysis - Vertical connection */}
        <WorkflowConnector
          startPoint={getConnectionPoint('cursorAI', 'top')}
          endPoint={getConnectionPoint('aiAnalysis', 'bottom')}
          color={colors.cursorAI}
          glowColor={colors.cursorAI}
          delay={900}
          curveType="up"
        />

        {/* Connection Points */}
        {Object.entries(cardPositions).map(([key, pos]) => {
          const color = colors[key as keyof typeof colors];
          const points = [];
          
          // Add connection points based on card position
          switch (key) {
            case 'aiAnalysis':
              points.push(getConnectionPoint(key, 'right'));
              points.push(getConnectionPoint(key, 'bottom'));
              break;
            case 'userFlow':
              points.push(getConnectionPoint(key, 'left'));
              points.push(getConnectionPoint(key, 'bottom'));
              break;
            case 'cursorAI':
              points.push(getConnectionPoint(key, 'right'));
              points.push(getConnectionPoint(key, 'top'));
              break;
            case 'smartKanban':
              points.push(getConnectionPoint(key, 'left'));
              points.push(getConnectionPoint(key, 'top'));
              break;
          }

          return points.map((point, index) => (
            <g key={`${key}-${index}`} filter="url(#glow)">
              {/* Connection point with glow */}
              <circle
                cx={point.x}
                cy={point.y}
                r="6"
                fill={color}
                opacity="0.3"
              />
              <circle
                cx={point.x}
                cy={point.y}
                r="3"
                fill={color}
                opacity="1"
              />
            </g>
          ));
        })}
      </svg>
    </div>
  );
} 