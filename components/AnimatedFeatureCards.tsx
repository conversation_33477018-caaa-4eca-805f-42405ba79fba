import { motion, useMotionValue, useTransform, AnimatePresence } from 'framer-motion';
import { RocketLaunchIcon, ChartBarIcon, QueueListIcon, CodeBracketIcon } from '@heroicons/react/24/outline';
import { useInView } from 'framer-motion';
import { useRef, useState } from 'react';

const features = [
  {
    title: "AI Analysis",
    description: "Advanced AI-powered analysis of your SaaS idea based on six core market pillars",
    icon: RocketLaunchIcon,
    color: "bg-blue-500/10",
    textColor: "text-blue-500",
    borderColor: "border-blue-500/20",
    glowColor: "from-blue-500/20",
    position: "left",
  },
  {
    title: "User Flow Designer",
    description: "Create sophisticated visual user flows with our premium diagram editor",
    icon: QueueListIcon,
    color: "bg-purple-500/10",
    textColor: "text-purple-500",
    borderColor: "border-purple-500/20",
    glowColor: "from-purple-500/20",
    position: "right",
  },
  {
    title: "Smart Kanban Board",
    description: "Intelligent project management with AI-generated tickets",
    icon: ChartBarIcon,
    color: "bg-green-500/10",
    textColor: "text-green-500",
    borderColor: "border-green-500/20",
    glowColor: "from-green-500/20",
    position: "right",
  },
  {
    title: "Cursor AI Integration",
    description: "Advanced coding assistance integrated directly into your workflow",
    icon: CodeBracketIcon,
    color: "bg-amber-500/10",
    textColor: "text-amber-500",
    borderColor: "border-amber-500/20",
    glowColor: "from-amber-500/20",
    position: "left",
  },
];

const cardVariants = {
  hidden: (position: string) => ({ 
    opacity: 0, 
    x: position === "left" ? -50 : 50,
    y: 20,
    scale: 0.9
  }),
  visible: (position: string) => ({
    opacity: 1,
    x: 0,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
      mass: 0.5,
    }
  }),
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  }
};

const iconVariants = {
  initial: { scale: 1, rotate: 0 },
  hover: { 
    scale: 1.2,
    rotate: 360,
    transition: {
      type: "spring",
      stiffness: 260,
      damping: 20
    }
  }
};

const AnimatedFeatureCard = ({ feature, index }: { feature: typeof features[0], index: number }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [isHovered, setIsHovered] = useState(false);
  
  // Mouse movement animation
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const rotateX = useTransform(mouseY, [-100, 100], [10, -10]);
  const rotateY = useTransform(mouseX, [-100, 100], [-10, 10]);

  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left - rect.width / 2;
    const y = event.clientY - rect.top - rect.height / 2;
    mouseX.set(x);
    mouseY.set(y);
  };

  const handleMouseLeave = () => {
    mouseX.set(0);
    mouseY.set(0);
    setIsHovered(false);
  };

  const Icon = feature.icon;

  return (
    <motion.div
      ref={ref}
      custom={feature.position}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      whileHover="hover"
      variants={cardVariants}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      style={{
        rotateX,
        rotateY,
        transformStyle: "preserve-3d",
      }}
      className={`relative p-6 rounded-2xl border backdrop-blur-xl transform-gpu ${feature.position === 'left' ? 'ml-8' : 'mr-8'}`}
    >
      {/* Background gradient */}
      <motion.div 
        className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.glowColor} to-transparent opacity-20`}
        style={{
          filter: "blur(20px)",
          transform: "translateZ(-1px)",
        }}
        animate={{
          opacity: isHovered ? 0.3 : 0.2,
        }}
      />

      {/* Card content */}
      <div className="relative z-10">
        <div className="flex items-center space-x-4">
          <motion.div
            variants={iconVariants}
            className={`p-3 rounded-lg ${feature.color} ${feature.textColor} relative`}
          >
            <Icon className="w-6 h-6 relative z-10" />
            {/* Icon glow effect */}
            <motion.div
              className={`absolute inset-0 rounded-lg ${feature.color}`}
              initial={{ scale: 1 }}
              animate={{ scale: isHovered ? 1.2 : 1 }}
              style={{ filter: "blur(8px)" }}
            />
          </motion.div>
          <div>
            <motion.h3 
              className={`text-lg font-semibold ${feature.textColor}`}
              animate={{ scale: isHovered ? 1.05 : 1 }}
            >
              {feature.title}
            </motion.h3>
            <p className="mt-1 text-sm text-gray-300">
              {feature.description}
            </p>
          </div>
        </div>
      </div>

      {/* Hover effects */}
      <AnimatePresence>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 -z-10"
          >
            {/* Animated border gradient */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/10 to-transparent animate-border-flow" />
            
            {/* Enhanced glow effect */}
            <motion.div 
              className={`absolute inset-0 rounded-2xl ${feature.color} opacity-30 blur-xl`}
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.3, 0.5, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default function AnimatedFeatureCards() {
  return (
    <div className="relative mx-auto max-w-6xl px-4">
      {/* Grid container with 12-column layout */}
      <div className="grid grid-cols-12 gap-12">
        {/* Left Column */}
        <div className="col-span-12 lg:col-span-6 flex flex-col space-y-32">
          {/* AI Analysis Card */}
          <div className="relative flex items-center justify-end" id="ai-analysis-card">
            <AnimatedFeatureCard 
              feature={features[0]} 
              index={0}
              className="w-full max-w-md transform hover:scale-105 transition-transform duration-300"
            />
            {/* Connector anchor point */}
            <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 z-10" />
          </div>

          {/* Cursor AI Card */}
          <div className="relative flex items-center justify-end" id="cursor-ai-card">
            <AnimatedFeatureCard 
              feature={features[3]} 
              index={3}
              className="w-full max-w-md transform hover:scale-105 transition-transform duration-300"
            />
            {/* Connector anchor point */}
            <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 z-10" />
          </div>
        </div>

        {/* Right Column */}
        <div className="col-span-12 lg:col-span-6 flex flex-col space-y-32 lg:mt-48">
          {/* User Flow Card */}
          <div className="relative flex items-center justify-start" id="user-flow-card">
            <AnimatedFeatureCard 
              feature={features[1]} 
              index={1}
              className="w-full max-w-md transform hover:scale-105 transition-transform duration-300"
            />
            {/* Connector anchor point */}
            <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 z-10" />
          </div>

          {/* Smart Kanban Card */}
          <div className="relative flex items-center justify-start" id="kanban-card">
            <AnimatedFeatureCard 
              feature={features[2]} 
              index={2}
              className="w-full max-w-md transform hover:scale-105 transition-transform duration-300"
            />
            {/* Connector anchor point */}
            <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 z-10" />
          </div>
        </div>
      </div>
    </div>
  );
} 