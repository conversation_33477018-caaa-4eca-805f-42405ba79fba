import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { useRef } from 'react';

interface EnhancedParallaxProps {
  children: React.ReactNode;
  offset?: number;
  className?: string;
}

const EnhancedParallax = ({ children, offset = 50, className = '' }: EnhancedParallaxProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start']
  });

  const springConfig = {
    damping: 15,
    stiffness: 100,
    mass: 0.8
  };

  const y = useSpring(
    useTransform(scrollYProgress, [0, 1], [offset, -offset]),
    springConfig
  );

  const scale = useSpring(
    useTransform(scrollYProgress, [0, 0.5, 1], [0.9, 1, 0.9]),
    springConfig
  );

  const opacity = useSpring(
    useTransform(scrollYProgress, [0, 0.5, 1], [0.6, 1, 0.6]),
    springConfig
  );

  return (
    <motion.div
      ref={ref}
      style={{
        y,
        scale,
        opacity
      }}
      className={`relative ${className}`}
    >
      {children}
    </motion.div>
  );
};

export default EnhancedParallax; 