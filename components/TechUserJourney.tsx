"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { CheckCircle2, LightbulbIcon, BarChart2, Figma, ListTodo, Code, Rocket } from 'lucide-react'

interface TechUserJourneyProps {
  className?: string
}

interface JourneyStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  status: 'completed' | 'active' | 'pending'
}

export default function TechUserJourney({ className = '' }: TechUserJourneyProps) {
  const [activeStep, setActiveStep] = useState(1)
  
  // Define journey steps
  const journeySteps: JourneyStep[] = [
    {
      id: 'ideate',
      title: 'Ideate',
      description: 'Start with your SaaS idea and let our AI analyze its potential',
      icon: <LightbulbIcon className="h-6 w-6" />,
      status: 'completed'
    },
    {
      id: 'analyze',
      title: 'Analyze',
      description: 'Get comprehensive AI-powered market analysis and feasibility scores',
      icon: <BarChart2 className="h-6 w-6" />,
      status: 'active'
    },
    {
      id: 'design',
      title: 'Design',
      description: 'Create user flows and wireframes with our visual design tools',
      icon: <Figma className="h-6 w-6" />,
      status: 'pending'
    },
    {
      id: 'plan',
      title: 'Plan',
      description: 'Organize development with AI-generated tasks and smart Kanban boards',
      icon: <ListTodo className="h-6 w-6" />,
      status: 'pending'
    },
    {
      id: 'build',
      title: 'Build',
      description: 'Code efficiently with Cursor AI integration and premium development tools',
      icon: <Code className="h-6 w-6" />,
      status: 'pending'
    },
    {
      id: 'launch',
      title: 'Launch',
      description: 'Deploy your SaaS with confidence using our launch optimization features',
      icon: <Rocket className="h-6 w-6" />,
      status: 'pending'
    }
  ]
  
  return (
    <div className={`w-full ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600/20">
            <svg className="h-5 w-5 text-blue-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L20 7V17L12 22L4 17V7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M20 7L12 12L4 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <h2 className="text-lg font-semibold uppercase tracking-wider text-blue-600">HOW IT WORKS</h2>
        </div>
        
        <h3 className="text-3xl font-bold text-white mb-8">Your SaaS Journey</h3>
        
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left column - Journey steps */}
          <div className="w-full lg:w-1/3 space-y-4">
            {journeySteps.map((step, index) => (
              <div 
                key={step.id}
                className={`
                  flex items-start p-4 rounded-lg border cursor-pointer transition-all
                  ${step.status === 'active' 
                    ? 'bg-slate-800/70 border-blue-500'
                    : 'bg-slate-900/40 border-slate-800 hover:border-slate-700'
                  }
                `}
                onClick={() => setActiveStep(index)}
              >
                <div className={`
                  flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center mr-4
                  ${step.status === 'completed' 
                    ? 'bg-green-500/20 text-green-500' 
                    : step.status === 'active' 
                      ? 'bg-blue-500/20 text-blue-400'
                      : 'bg-slate-800 text-slate-400'
                  }
                `}>
                  {step.status === 'completed' ? (
                    <CheckCircle2 className="h-5 w-5" />
                  ) : (
                    step.icon
                  )}
                </div>
                
                <div>
                  <div className="flex items-center">
                    <h4 className={`font-semibold ${
                      step.status !== 'pending' ? 'text-white' : 'text-slate-300'
                    }`}>
                      {step.title}
                    </h4>
                    
                    {step.status === 'completed' && (
                      <span className="ml-2 text-xs font-medium bg-green-500/20 text-green-500 px-2 py-0.5 rounded-full">
                        Completed
                      </span>
                    )}
                    
                    {step.status === 'active' && (
                      <span className="ml-2 text-xs font-medium bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded-full">
                        Active
                      </span>
                    )}
                  </div>
                  
                  <p className={`text-sm mt-1 ${
                    step.status !== 'pending' ? 'text-slate-300' : 'text-slate-400'
                  }`}>
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          {/* Right column - Market feasibility */}
          <div className="w-full lg:w-2/3">
            <div className="rounded-xl bg-slate-900/80 backdrop-blur-sm border border-white/10 overflow-hidden shadow-xl">
              <div className="p-6">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-2xl font-bold text-white">Market Feasibility</h3>
                    <p className="text-slate-400">AI Analysis Preview</p>
                  </div>
                  <div className="text-4xl font-bold text-blue-500">
                    8.2<span className="text-lg text-slate-400">/10</span>
                  </div>
                </div>
                
                <div className="space-y-6">
                  {[
                    { name: 'Uniqueness', description: 'Market differentiation', value: 85, color: 'from-green-500 to-emerald-500' },
                    { name: 'Stickiness', description: 'User retention potential', value: 92, color: 'from-blue-500 to-cyan-500' },
                    { name: 'Growth', description: 'Scalability factor', value: 78, color: 'from-purple-500 to-violet-500' },
                    { name: 'Pricing', description: 'Revenue optimization', value: 88, color: 'from-emerald-500 to-teal-500' }
                  ].map((metric) => (
                    <div key={metric.name} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="text-white font-medium">{metric.name}</h4>
                          <p className="text-xs text-slate-400">{metric.description}</p>
                        </div>
                        <span className="text-white font-bold">{metric.value}%</span>
                      </div>
                      <div className="h-2 w-full bg-slate-800 rounded-full overflow-hidden">
                        <motion.div 
                          className={`h-full bg-gradient-to-r ${metric.color}`}
                          initial={{ width: 0 }}
                          animate={{ width: `${metric.value}%` }}
                          transition={{ duration: 1, delay: 0.3 }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-8 text-center">
                  <p className="text-sm text-slate-400 mb-4">
                    See how your idea scores with our AI analysis
                  </p>
                  <button className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                    Analyze My Idea
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 