"use client"

import * as React from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { Button } from "@/components/ui/button"
import { Menu } from "lucide-react"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

export default function Navbar() {
  const [visible, setVisible] = React.useState(true);
  const [hasScrolled, setHasScrolled] = React.useState(false);
  const lastScrollY = React.useRef(0);

  React.useEffect(() => {
    // Set initial scroll position after component mounts
    lastScrollY.current = window.scrollY;
    
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          const currentScroll = window.scrollY;
          const scrolledDown = currentScroll > lastScrollY.current;
          
          // Show/hide navbar based on scroll direction and position
          if (scrolledDown && currentScroll > 50) {
            setVisible(false);
            setHasScrolled(true);
          } else if (!scrolledDown) {
            setVisible(true);
          }

          lastScrollY.current = currentScroll;
          ticking = false;
        });
      }
      ticking = true;
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Section navigation handler
  const handleSectionClick = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
    e.preventDefault();
    const section = document.getElementById(id);
    
    if (section) {
      const navHeight = 80; // Adjusted offset for better section visibility
      const sectionTop = section.offsetTop - navHeight;
      
      // Ensure we're scrolling to a valid position
      const scrollPosition = Math.max(0, sectionTop);
      
      // First make navbar visible if it was hidden
      setVisible(true);
      
      // Jump directly to the section without smooth scrolling
      window.scrollTo({
        top: scrollPosition
      });
    }
  };

  return (
    <>
      {/* Gradient Overlay */}
      <div 
        className="pointer-events-none fixed z-40 h-32 w-full bg-gradient-to-b from-[#0d0d0d] via-[#0d0d0d]/80 to-transparent" 
        style={{ top: 0 }}
      />
      
      {/* Navbar Container */}
      <div 
        className={`
          fixed top-4 left-0 right-0 z-50 w-full px-4
          transform transition-all duration-300 ease-in-out
          ${!visible ? '-translate-y-full opacity-0' : 'translate-y-0 opacity-100'}
          ${hasScrolled ? 'will-change-transform' : ''}
        `}
      >
        <header className="mx-auto max-w-7xl rounded-2xl bg-[#0d0d0d]/90 backdrop-blur-xl border border-white/10">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex h-14 items-center justify-between">
              {/* Logo */}
              <div className="flex items-center">
                <Link href="/" className="flex items-center">
                  <span className="font-display font-bold tracking-wide text-xl text-white">SassifyX</span>
                </Link>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden lg:flex lg:items-center lg:justify-end lg:flex-1 lg:space-x-4">
                <NavigationMenu className="mr-4">
                  <NavigationMenuList className="flex space-x-1">
                    <NavigationMenuItem>
                      <NavigationMenuTrigger className="bg-transparent text-gray-300 hover:text-white hover:bg-white/5">
                        Features
                      </NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-2 bg-[#0d0d0d]/90 backdrop-blur-xl border border-white/10 rounded-xl">
                          <ListItem href="#features" title="AI Analysis" icon="✨">
                            Advanced AI-powered analysis of your SaaS idea
                          </ListItem>
                          <ListItem href="#features" title="User Flow" icon="🔄">
                            Create sophisticated visual user flows
                          </ListItem>
                          <ListItem href="#features" title="Kanban Board" icon="📋">
                            Smart project management with AI tickets
                          </ListItem>
                          <ListItem href="#features" title="Cursor AI" icon="🤖">
                            Premium AI coding assistance
                          </ListItem>
                        </ul>
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                    <NavigationMenuItem>
                      <a
                        href="#how-it-works"
                        onClick={(e) => handleSectionClick(e, 'how-it-works')}
                        className={cn(navigationMenuTriggerStyle(), "bg-transparent text-gray-300 hover:text-white hover:bg-white/5 cursor-pointer")}
                      >
                        How It Works
                      </a>
                    </NavigationMenuItem>
                    <NavigationMenuItem>
                      <a
                        href="#pricing"
                        onClick={(e) => handleSectionClick(e, 'pricing')}
                        className={cn(navigationMenuTriggerStyle(), "bg-transparent text-gray-300 hover:text-white hover:bg-white/5 cursor-pointer")}
                      >
                        Pricing
                      </a>
                    </NavigationMenuItem>
                    <NavigationMenuItem>
                      <a
                        href="#about"
                        onClick={(e) => handleSectionClick(e, 'about')}
                        className={cn(navigationMenuTriggerStyle(), "bg-transparent text-gray-300 hover:text-white hover:bg-white/5 cursor-pointer")}
                      >
                        About
                      </a>
                    </NavigationMenuItem>
                  </NavigationMenuList>
                </NavigationMenu>

                <div className="flex items-center space-x-3">
                  <Button variant="ghost" asChild className="text-gray-300 hover:text-white hover:bg-white/5">
                    <Link href="/auth/signin">Sign In</Link>
                  </Button>
                  <Button
                    asChild
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg rounded-lg px-6"
                  >
                    <Link href="/auth/signup">Get Started</Link>
                  </Button>
                </div>
              </div>

              {/* Mobile Navigation */}
              <div className="flex lg:hidden">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white hover:bg-white/5">
                      <Menu className="h-5 w-5" />
                      <span className="sr-only">Toggle menu</span>
                    </Button>
                  </SheetTrigger>
                  <SheetContent
                    side="right"
                    className="w-[300px] sm:w-[400px] bg-[#0d0d0d]/90 backdrop-blur-xl border-white/10"
                  >
                    <nav className="flex flex-col gap-4">
                      <a
                        href="#features"
                        onClick={(e) => handleSectionClick(e, 'features')}
                        className="block px-2 py-1 text-lg font-medium text-gray-300 hover:text-white hover:bg-white/5 cursor-pointer"
                      >
                        Features
                      </a>
                      <a
                        href="#how-it-works"
                        onClick={(e) => handleSectionClick(e, 'how-it-works')}
                        className="block px-2 py-1 text-lg font-medium text-gray-300 hover:text-white hover:bg-white/5 cursor-pointer"
                      >
                        How It Works
                      </a>
                      <a
                        href="#pricing"
                        onClick={(e) => handleSectionClick(e, 'pricing')}
                        className="block px-2 py-1 text-lg font-medium text-gray-300 hover:text-white hover:bg-white/5 cursor-pointer"
                      >
                        Pricing
                      </a>
                      <a
                        href="#about"
                        onClick={(e) => handleSectionClick(e, 'about')}
                        className="block px-2 py-1 text-lg font-medium text-gray-300 hover:text-white hover:bg-white/5 cursor-pointer"
                      >
                        About
                      </a>
                      <div className="flex flex-col gap-3 mt-6">
                        <Button
                          variant="ghost"
                          asChild
                          className="w-full text-gray-300 hover:text-white hover:bg-white/5"
                        >
                          <Link href="/auth/signin">Sign In</Link>
                        </Button>
                        <Button
                          asChild
                          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg"
                        >
                          <Link href="/auth/signup">Get Started</Link>
                        </Button>
                      </div>
                    </nav>
                  </SheetContent>
                </Sheet>
              </div>
            </div>
          </div>
        </header>
      </div>
    </>
  );
}

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & { icon?: string; title: string }
>(({ className, title, icon, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-white/5 focus:bg-white/5 text-gray-300 hover:text-white",
            className,
          )}
          {...props}
        >
          <div className="flex items-center gap-2 text-sm font-medium leading-none">
            {icon && <span>{icon}</span>}
            {title}
          </div>
          <p className="line-clamp-2 text-sm leading-snug text-gray-400">{children}</p>
        </a>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"
