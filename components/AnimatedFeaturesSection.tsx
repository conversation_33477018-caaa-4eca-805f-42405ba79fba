import { useRef, useEffect, useState } from 'react';
import AnimatedFeatureCards from './AnimatedFeatureCards';
import WorkflowConnectors from './WorkflowConnectors';
import { motion } from 'framer-motion';

export default function AnimatedFeaturesSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      {
        threshold: 0.1,
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <motion.section
      ref={sectionRef}
      className="relative overflow-hidden bg-gradient-to-b from-gray-900 via-gray-900 to-gray-800 py-32"
      initial={{ opacity: 0 }}
      animate={{ 
        opacity: isVisible ? 1 : 0,
        transition: { duration: 0.5 }
      }}
    >
      {/* Background gradient circles */}
      <motion.div
        className="absolute inset-0 overflow-hidden pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ 
          opacity: isVisible ? 0.6 : 0,
          transition: { duration: 1, delay: 0.5 }
        }}
      >
        {/* Top right gradient */}
        <div className="absolute -top-40 right-1/4 w-96 h-96 bg-purple-500/30 rounded-full mix-blend-overlay filter blur-3xl animate-blob" />
        
        {/* Bottom left gradient */}
        <div className="absolute -bottom-40 left-1/4 w-96 h-96 bg-blue-500/30 rounded-full mix-blend-overlay filter blur-3xl animate-blob animation-delay-2000" />
        
        {/* Center gradient */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-500/30 rounded-full mix-blend-overlay filter blur-3xl animate-blob animation-delay-4000" />
      </motion.div>

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:14px_24px]" />

      {/* Radial gradient overlay */}
      <div className="absolute inset-0 bg-gradient-radial from-transparent via-gray-900/90 to-gray-900" />

      {/* Main content */}
      <div className="relative z-10">
        <AnimatedFeatureCards />
        <WorkflowConnectors />
      </div>

      {/* Animated particles */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ 
          opacity: isVisible ? 1 : 0,
          transition: { duration: 1 }
        }}
      >
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 2 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "easeInOut",
            }}
          />
        ))}
      </motion.div>

      {/* Glowing lines */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent"
            style={{
              width: '200%',
              left: '-50%',
              top: `${20 + (i * 15)}%`,
            }}
            animate={{
              x: ['-100%', '100%'],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 4,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "linear",
            }}
          />
        ))}
      </div>
    </motion.section>
  );
} 