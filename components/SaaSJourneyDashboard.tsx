"use client"

import React from 'react'
import { Rocket } from 'lucide-react'
import MarketFeasibilityPreview from './MarketFeasibilityPreview'
import UserJourneySteps from './UserJourneySteps'

interface SaaSJourneyDashboardProps {
  className?: string
}

export default function SaaSJourneyDashboard({ className = '' }: SaaSJourneyDashboardProps) {
  return (
    <div className={`w-full ${className}`}>
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600/20">
            <svg className="h-5 w-5 text-blue-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L20 7V17L12 22L4 17V7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M20 7L12 12L4 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <h2 className="text-lg font-semibold uppercase tracking-wider text-blue-600">HOW IT WORKS</h2>
        </div>
        
        <h3 className="text-3xl font-bold text-white mb-8">Your SaaS Journey</h3>
        
        {/* Top banner */}
        <div className="relative w-full mb-12">
          <div className="rounded-lg bg-gradient-to-r from-blue-900/30 to-purple-900/30 border border-blue-800/30 p-4 sm:p-6">
            <div className="flex items-center">
              <Rocket className="h-6 w-6 text-blue-400 mr-4" />
              <p className="text-blue-100 text-sm sm:text-base">
                Validate and build your SaaS ideas efficiently
              </p>
            </div>
          </div>
        </div>
        
        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left column - SaaS intro */}
          <div className="lg:col-span-5 space-y-8">
            <div>
              <h2 className="text-5xl sm:text-6xl font-bold text-white mb-6">
                SassifyX
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">
                  ideas into
                </span>
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-blue-500">
                  reality
                </span>
              </h2>
              
              <h3 className="text-2xl font-medium text-white mb-4">
                Sketch it. <span className="text-blue-400">SaaSify</span> it. <span className="text-purple-400">Xecute</span> it.
              </h3>
              
              <p className="text-slate-300 mb-8">
                AI-powered platform that analyzes, validates, and helps you build successful SaaS applications from concept to launch.
              </p>
              
              <div className="flex flex-wrap gap-4">
                <button className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                  Get Started Free
                </button>
                <button className="px-6 py-3 bg-transparent border border-slate-700 hover:border-slate-600 text-white font-medium rounded-lg transition-colors">
                  Explore Features
                </button>
              </div>
            </div>
          </div>
          
          {/* Right column - Dashboard preview */}
          <div className="lg:col-span-7 grid grid-cols-1 md:grid-cols-12 gap-6">
            {/* Market feasibility preview */}
            <div className="md:col-span-7">
              <MarketFeasibilityPreview />
            </div>
            
            {/* User journey steps */}
            <div className="md:col-span-5">
              <UserJourneySteps />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 