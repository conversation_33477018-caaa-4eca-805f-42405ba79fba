"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'

interface FlowDiagramProps {
  className?: string
}

interface FlowNode {
  id: string
  label: string
  type: 'decision' | 'process'
  x: number
  y: number
  connections: {
    to: string
    label?: string
    type: 'yes' | 'no' | 'normal'
  }[]
}

export default function FlowDiagram({ className = '' }: FlowDiagramProps) {
  const [activeNodes, setActiveNodes] = useState<string[]>(['viable', 'refine-idea', 'design-user-flow', 'project-planning', 'development', 'testing', 'ready', 'launch'])
  
  const nodes: FlowNode[] = [
    {
      id: 'viable',
      label: 'Viable?',
      type: 'decision',
      x: 300,
      y: 100,
      connections: [
        { to: 'refine-idea', label: 'no', type: 'no' },
        { to: 'design-user-flow', label: 'yes', type: 'yes' }
      ]
    },
    {
      id: 'refine-idea',
      label: 'Refine Idea',
      type: 'process',
      x: 500,
      y: 100,
      connections: []
    },
    {
      id: 'design-user-flow',
      label: 'Design User Flow',
      type: 'process',
      x: 300,
      y: 250,
      connections: [
        { to: 'project-planning', type: 'normal' }
      ]
    },
    {
      id: 'project-planning',
      label: 'Project Planning',
      type: 'process',
      x: 500,
      y: 250,
      connections: [
        { to: 'development', type: 'normal' }
      ]
    },
    {
      id: 'development',
      label: 'Development',
      type: 'process',
      x: 500,
      y: 370,
      connections: [
        { to: 'testing', type: 'normal' }
      ]
    },
    {
      id: 'testing',
      label: 'Testing',
      type: 'process',
      x: 700,
      y: 370,
      connections: [
        { to: 'ready', type: 'normal' }
      ]
    },
    {
      id: 'ready',
      label: 'Ready?',
      type: 'decision',
      x: 700,
      y: 500,
      connections: [
        { to: 'development', label: 'no', type: 'no' },
        { to: 'launch', label: 'yes', type: 'yes' }
      ]
    },
    {
      id: 'launch',
      label: 'Launch',
      type: 'process',
      x: 700,
      y: 650,
      connections: []
    }
  ]
  
  const toggleNode = (id: string) => {
    setActiveNodes(prev => 
      prev.includes(id)
        ? prev.filter(nodeId => nodeId !== id)
        : [...prev, id]
    )
  }
  
  // Function to generate an SVG path between two nodes
  const generatePath = (from: FlowNode, to: FlowNode, type: 'yes' | 'no' | 'normal') => {
    const fromX = from.x + 60 // center of node
    const fromY = from.y + (from.type === 'decision' ? 50 : 30) // bottom of node
    
    const toX = to.x + 60 // center of node
    const toY = to.y // top of node
    
    if (type === 'yes') {
      // Vertical path for 'yes' connections
      return `M ${fromX} ${fromY} L ${fromX} ${toY}`
    } else if (type === 'no') {
      // Horizontal path for 'no' connections
      const midY = (fromY + toY) / 2
      return `M ${fromX} ${fromY - 30} L ${toX} ${fromY - 30} L ${toX} ${toY}`
    } else {
      // Normal connection
      return `M ${fromX} ${fromY} L ${toX} ${toY}`
    }
  }
  
  return (
    <div className={`relative w-full overflow-auto ${className}`}>
      <div className="flex justify-center mb-4">
        <div className="inline-flex rounded-md shadow-sm" role="group">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium rounded-l-lg bg-slate-800 text-slate-300 hover:bg-slate-700"
          >
            Step-by-Step View
          </button>
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium rounded-r-lg bg-blue-600 text-white"
          >
            Flow Diagram
          </button>
        </div>
      </div>
      
      <div className="relative w-full min-w-[800px] h-[700px] bg-slate-950/50 rounded-xl border border-slate-800 p-6">
        <svg width="100%" height="100%" viewBox="0 0 800 700">
          {/* Render connections */}
          {nodes.map(node => 
            node.connections.map(conn => {
              const targetNode = nodes.find(n => n.id === conn.to)
              if (!targetNode) return null
              
              const isActive = activeNodes.includes(node.id) && activeNodes.includes(targetNode.id)
              
              return (
                <g key={`${node.id}-${conn.to}`}>
                  {/* Connection path */}
                  <path
                    d={generatePath(node, targetNode, conn.type)}
                    stroke={isActive ? '#3b82f6' : '#475569'}
                    strokeWidth={2}
                    fill="none"
                    className="transition-colors duration-300"
                  />
                  
                  {/* Connection label */}
                  {conn.label && (
                    <text
                      x={conn.type === 'yes' ? node.x + 70 : (node.x + targetNode.x) / 2}
                      y={conn.type === 'yes' ? (node.y + targetNode.y) / 2 : node.y - 40}
                      fill={isActive ? '#3b82f6' : '#64748b'}
                      fontSize="14"
                      textAnchor="middle"
                      className="transition-colors duration-300"
                    >
                      {conn.label}
                    </text>
                  )}
                  
                  {/* Animated circle on active connections */}
                  {isActive && (
                    <motion.circle
                      r={4}
                      fill="#3b82f6"
                      initial={{ offset: 0 }}
                      animate={{ offset: 1 }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                    >
                      <animateMotion
                        dur="1.5s"
                        repeatCount="indefinite"
                        path={generatePath(node, targetNode, conn.type)}
                      />
                    </motion.circle>
                  )}
                </g>
              )
            })
          )}
          
          {/* Render nodes */}
          {nodes.map(node => {
            const isActive = activeNodes.includes(node.id)
            
            return (
              <g 
                key={node.id} 
                onClick={() => toggleNode(node.id)}
                className="cursor-pointer"
              >
                {/* Node shape */}
                {node.type === 'decision' ? (
                  <polygon
                    points={`
                      ${node.x},${node.y + 50}
                      ${node.x + 60},${node.y}
                      ${node.x + 120},${node.y + 50}
                      ${node.x + 60},${node.y + 100}
                    `}
                    fill={isActive ? '#8b5cf6' : '#1e293b'}
                    stroke={isActive ? '#a78bfa' : '#334155'}
                    strokeWidth={2}
                    className="transition-colors duration-300"
                  />
                ) : (
                  <rect
                    x={node.x}
                    y={node.y}
                    width={120}
                    height={60}
                    rx={5}
                    fill={isActive ? '#4f46e5' : '#1e293b'}
                    stroke={isActive ? '#818cf8' : '#334155'}
                    strokeWidth={2}
                    className="transition-colors duration-300"
                  />
                )}
                
                {/* Node label */}
                <text
                  x={node.x + 60}
                  y={node.y + (node.type === 'decision' ? 50 : 30)}
                  fill="white"
                  fontSize="14"
                  fontWeight={isActive ? "bold" : "normal"}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className="pointer-events-none transition-all duration-300"
                >
                  {node.label}
                </text>
                
                {/* Active indicator */}
                {isActive && (
                  <circle
                    cx={node.x + 10}
                    cy={node.y + 10}
                    r={5}
                    fill="#10b981"
                  >
                    <animate
                      attributeName="opacity"
                      values="0.5;1;0.5"
                      dur="2s"
                      repeatCount="indefinite"
                    />
                  </circle>
                )}
              </g>
            )
          })}
        </svg>
      </div>
    </div>
  )
} 