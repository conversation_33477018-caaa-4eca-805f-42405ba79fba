"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronRight, ArrowRight, ArrowDown, Zap, Check, X } from 'lucide-react'

interface UserFlowDiagramProps {
  activeStep: number
  className?: string
}

type FlowNode = {
  id: string
  label: string
  type: 'start' | 'process' | 'decision' | 'end'
  x: number
  y: number
  width: number
  height: number
  connections: {
    to: string
    label?: string
    path?: 'right' | 'down'
    condition?: 'yes' | 'no'
  }[]
  step: number
}

export default function UserFlowDiagram({ activeStep, className = '' }: UserFlowDiagramProps) {
  const [highlightedNodes, setHighlightedNodes] = useState<string[]>([])
  const [highlightedPaths, setHighlightedPaths] = useState<string[]>([])
  
  // Define the flow nodes
  const nodes: FlowNode[] = [
    {
      id: 'idea',
      label: 'SaaS Idea',
      type: 'start',
      x: 0,
      y: 0,
      width: 120,
      height: 60,
      connections: [{ to: 'analysis', path: 'right' }],
      step: 1
    },
    {
      id: 'analysis',
      label: 'AI Analysis',
      type: 'process',
      x: 200,
      y: 0,
      width: 120,
      height: 60,
      connections: [{ to: 'viable', path: 'down' }],
      step: 2
    },
    {
      id: 'viable',
      label: 'Viable?',
      type: 'decision',
      x: 200,
      y: 120,
      width: 100,
      height: 100,
      connections: [
        { to: 'refine', path: 'right', condition: 'no' },
        { to: 'userflow', path: 'down', condition: 'yes' }
      ],
      step: 2
    },
    {
      id: 'refine',
      label: 'Refine Idea',
      type: 'process',
      x: 380,
      y: 140,
      width: 120,
      height: 60,
      connections: [{ to: 'analysis', path: 'right' }],
      step: 2
    },
    {
      id: 'userflow',
      label: 'Design User Flow',
      type: 'process',
      x: 200,
      y: 280,
      width: 120,
      height: 60,
      connections: [{ to: 'planning', path: 'right' }],
      step: 3
    },
    {
      id: 'planning',
      label: 'Project Planning',
      type: 'process',
      x: 400,
      y: 280,
      width: 120,
      height: 60,
      connections: [{ to: 'development', path: 'down' }],
      step: 4
    },
    {
      id: 'development',
      label: 'Development',
      type: 'process',
      x: 400,
      y: 400,
      width: 120,
      height: 60,
      connections: [{ to: 'testing', path: 'right' }],
      step: 5
    },
    {
      id: 'testing',
      label: 'Testing',
      type: 'process',
      x: 600,
      y: 400,
      width: 120,
      height: 60,
      connections: [{ to: 'ready', path: 'down' }],
      step: 5
    },
    {
      id: 'ready',
      label: 'Ready?',
      type: 'decision',
      x: 600,
      y: 520,
      width: 100,
      height: 100,
      connections: [
        { to: 'development', path: 'right', condition: 'no' },
        { to: 'launch', path: 'down', condition: 'yes' }
      ],
      step: 5
    },
    {
      id: 'launch',
      label: 'Launch',
      type: 'end',
      x: 600,
      y: 680,
      width: 120,
      height: 60,
      connections: [],
      step: 6
    }
  ]
  
  // Update highlighted nodes based on active step
  useEffect(() => {
    const relevantNodes = nodes
      .filter(node => node.step <= activeStep)
      .map(node => node.id)
    
    setHighlightedNodes(relevantNodes)
    
    // Highlight paths
    const paths: string[] = []
    nodes
      .filter(node => node.step <= activeStep)
      .forEach(node => {
        node.connections.forEach(conn => {
          // Only highlight paths if both nodes are highlighted
          if (relevantNodes.includes(conn.to)) {
            paths.push(`${node.id}-${conn.to}`)
          }
        })
      })
    
    setHighlightedPaths(paths)
  }, [activeStep])
  
  // Generate SVG path between nodes
  const generatePath = (from: FlowNode, to: FlowNode, path: 'right' | 'down' = 'right') => {
    const fromX = from.x + from.width / 2
    const fromY = from.y + from.height / 2
    const toX = to.x + to.width / 2
    const toY = to.y + to.height / 2
    
    if (path === 'right') {
      // For right paths, create a curved path
      const midX = (fromX + toX) / 2
      return `M ${fromX} ${fromY} C ${midX} ${fromY}, ${midX} ${toY}, ${toX} ${toY}`
    } else {
      // For down paths, create a straight path
      return `M ${fromX} ${fromY} L ${fromX} ${toY} L ${toX} ${toY}`
    }
  }
  
  return (
    <div className={`relative overflow-auto ${className}`}>
      <div className="w-full min-w-[800px] h-[800px] relative">
        <svg width="100%" height="100%" viewBox="0 0 800 800" className="absolute inset-0">
          {/* Render connections */}
          {nodes.map(node => 
            node.connections.map(conn => {
              const targetNode = nodes.find(n => n.id === conn.to)
              if (!targetNode) return null
              
              const pathId = `${node.id}-${conn.to}`
              const isHighlighted = highlightedPaths.includes(pathId)
              
              return (
                <g key={pathId}>
                  {/* Path */}
                  <path
                    d={generatePath(node, targetNode, conn.path)}
                    fill="none"
                    stroke={isHighlighted ? '#3b82f6' : '#334155'}
                    strokeWidth={isHighlighted ? 3 : 2}
                    strokeDasharray={isHighlighted ? 'none' : '5,5'}
                    className="transition-all duration-500"
                  />
                  
                  {/* Arrow */}
                  {isHighlighted && (
                    <motion.circle
                      initial={{ offset: 0 }}
                      animate={{ offset: 1 }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      r={6}
                      fill="#3b82f6"
                    >
                      <animateMotion
                        dur="1.5s"
                        repeatCount="indefinite"
                        path={generatePath(node, targetNode, conn.path)}
                      />
                    </motion.circle>
                  )}
                  
                  {/* Condition label */}
                  {conn.condition && (
                    <text
                      x={conn.path === 'right' 
                        ? (node.x + targetNode.x) / 2 
                        : node.x + node.width + 10}
                      y={conn.path === 'right'
                        ? (node.y + targetNode.y) / 2 - 10
                        : (node.y + targetNode.y) / 2}
                      fill={isHighlighted ? '#3b82f6' : '#64748b'}
                      fontSize="12"
                      textAnchor="middle"
                      className="transition-all duration-500"
                    >
                      {conn.condition}
                    </text>
                  )}
                </g>
              )
            })
          )}
          
          {/* Render nodes */}
          {nodes.map(node => {
            const isHighlighted = highlightedNodes.includes(node.id)
            
            // Determine shape based on node type
            let shape
            switch (node.type) {
              case 'start':
                shape = (
                  <rect
                    x={node.x}
                    y={node.y}
                    width={node.width}
                    height={node.height}
                    rx={30}
                    ry={30}
                    fill={isHighlighted ? '#3b82f6' : '#1e293b'}
                    stroke={isHighlighted ? '#60a5fa' : '#334155'}
                    strokeWidth={2}
                    className="transition-all duration-500"
                  />
                )
                break
              case 'end':
                shape = (
                  <rect
                    x={node.x}
                    y={node.y}
                    width={node.width}
                    height={node.height}
                    rx={30}
                    ry={30}
                    fill={isHighlighted ? '#10b981' : '#1e293b'}
                    stroke={isHighlighted ? '#34d399' : '#334155'}
                    strokeWidth={2}
                    className="transition-all duration-500"
                  />
                )
                break
              case 'decision':
                shape = (
                  <polygon
                    points={`
                      ${node.x + node.width / 2},${node.y}
                      ${node.x + node.width},${node.y + node.height / 2}
                      ${node.x + node.width / 2},${node.y + node.height}
                      ${node.x},${node.y + node.height / 2}
                    `}
                    fill={isHighlighted ? '#8b5cf6' : '#1e293b'}
                    stroke={isHighlighted ? '#a78bfa' : '#334155'}
                    strokeWidth={2}
                    className="transition-all duration-500"
                  />
                )
                break
              case 'process':
              default:
                shape = (
                  <rect
                    x={node.x}
                    y={node.y}
                    width={node.width}
                    height={node.height}
                    rx={5}
                    ry={5}
                    fill={isHighlighted ? '#4f46e5' : '#1e293b'}
                    stroke={isHighlighted ? '#818cf8' : '#334155'}
                    strokeWidth={2}
                    className="transition-all duration-500"
                  />
                )
            }
            
            return (
              <g key={node.id}>
                {shape}
                <text
                  x={node.x + node.width / 2}
                  y={node.y + node.height / 2}
                  fill="white"
                  fontSize="14"
                  fontWeight={isHighlighted ? "bold" : "normal"}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className="transition-all duration-500 pointer-events-none"
                >
                  {node.label}
                </text>
                
                {/* Step indicator */}
                {isHighlighted && (
                  <circle
                    cx={node.x + node.width - 10}
                    cy={node.y + 10}
                    r={8}
                    fill="#10b981"
                    className="transition-all duration-500"
                  >
                    <animate
                      attributeName="opacity"
                      values="0.5;1;0.5"
                      dur="2s"
                      repeatCount="indefinite"
                    />
                  </circle>
                )}
              </g>
            )
          })}
        </svg>
      </div>
    </div>
  )
} 