"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react'
import Image from 'next/image'

interface FeatureSlide {
  id: string
  title: string
  description: string
  imageSrc: string
  alt: string
}

interface FeatureDemonstrationProps {
  className?: string
}

export default function FeatureDemonstration({ className = '' }: FeatureDemonstrationProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [imageError, setImageError] = useState<Record<string, boolean>>({})
  
  // Feature slides data with real screenshots
  const slides: FeatureSlide[] = [
    {
      id: 'login',
      title: 'Secure Login',
      description: 'Access your SaaSifyX account with our secure authentication system.',
      imageSrc: '/login.png',
      alt: 'SaaSifyX login screen'
    },
    {
      id: 'signup',
      title: 'Easy Registration',
      description: 'Get started with SaaSifyX in seconds with our streamlined signup process.',
      imageSrc: '/images/demo/signup.png',
      alt: 'SaaSifyX signup screen'
    },
    {
      id: 'dashboard',
      title: 'Comprehensive Dashboard',
      description: 'Track your SaaS projects, completion rates, and time saved with our intuitive dashboard.',
      imageSrc: '/images/demo/dashboard.png',
      alt: 'SaaSifyX dashboard showing project metrics and progress'
    },
    {
      id: 'ai-insights',
      title: 'AI-Powered Insights',
      description: 'Leverage advanced AI analytics to gain valuable insights about your SaaS projects and their potential.',
      imageSrc: '/images/demo/ai-insights.png',
      alt: 'AI insights screen showing project analysis'
    },
    {
      id: 'idea-validator',
      title: 'Idea Validation',
      description: 'Validate your SaaS idea with comprehensive market analysis, technical feasibility, and business viability checks.',
      imageSrc: '/images/demo/idea-validator.png',
      alt: 'Idea validator screen showing validation options'
    },
    {
      id: 'settings',
      title: 'Customizable Settings',
      description: 'Configure your SaaSifyX profile and account settings to match your workflow and preferences.',
      imageSrc: '/images/demo/settings.png',
      alt: 'Settings screen showing profile configuration'
    }
  ]
  
  // Auto-advance slides when playing
  useEffect(() => {
    if (!isPlaying) return
    
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)
    
    return () => clearInterval(interval)
  }, [isPlaying, slides.length])
  
  // Navigation functions
  const goToNextSlide = () => {
    setIsPlaying(false)
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }
  
  const goToPrevSlide = () => {
    setIsPlaying(false)
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }
  
  const goToSlide = (index: number) => {
    setIsPlaying(false)
    setCurrentSlide(index)
  }
  
  // Handle image error
  const handleImageError = (id: string) => {
    setImageError(prev => ({ ...prev, [id]: true }))
  }
  
  return (
    <div className={`w-full ${className}`}>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold gradient-text mb-4">See SaaSifyX in Action</h2>
          <p className="text-lg text-slate-300 max-w-2xl mx-auto">
            Explore our powerful features designed to help you validate and build your SaaS ideas efficiently
          </p>
        </div>
        
        {/* Slideshow */}
        <div className="relative bg-slate-900/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden shadow-xl">
          {/* Slide content */}
          <div className="relative aspect-video">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 h-full">
                  {/* Image */}
                  <div className="relative h-full">
                    <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/50 to-transparent z-10"></div>
                    {imageError[slides[currentSlide].id] ? (
                      <div className="w-full h-full flex items-center justify-center bg-slate-800">
                        <div className="text-center p-6">
                          <div className="text-4xl mb-4">{slides[currentSlide].id === 'login' ? '🔐' : '📱'}</div>
                          <p className="text-white text-lg">{slides[currentSlide].title}</p>
                          <p className="text-slate-400 text-sm mt-2">Image preview coming soon</p>
                        </div>
                      </div>
                    ) : (
                      <img
                        src={slides[currentSlide].imageSrc}
                        alt={slides[currentSlide].alt}
                        className="w-full h-full object-cover"
                        onError={() => handleImageError(slides[currentSlide].id)}
                      />
                    )}
                  </div>
                  
                  {/* Description */}
                  <div className="flex flex-col justify-center p-6 md:p-12">
                    <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                      {slides[currentSlide].title}
                    </h3>
                    <p className="text-lg text-slate-300 mb-8">
                      {slides[currentSlide].description}
                    </p>
                    
                    {/* Feature number indicator */}
                    <div className="text-sm text-slate-400">
                      Feature {currentSlide + 1} of {slides.length}
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
          
          {/* Navigation controls */}
          <div className="absolute bottom-4 left-0 right-0 flex items-center justify-between px-4">
            <div className="flex space-x-2">
              <button
                onClick={() => setIsPlaying(!isPlaying)}
                className="p-2 rounded-full bg-slate-800/80 hover:bg-slate-700/80 text-white transition-colors"
                aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}
              >
                {isPlaying ? <Pause size={16} /> : <Play size={16} />}
              </button>
            </div>
            
            <div className="flex items-center space-x-2">
              {slides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`h-2 w-2 rounded-full transition-all ${
                    currentSlide === index ? 'bg-blue-500 w-6' : 'bg-slate-600 hover:bg-slate-500'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={goToPrevSlide}
                className="p-2 rounded-full bg-slate-800/80 hover:bg-slate-700/80 text-white transition-colors"
                aria-label="Previous slide"
              >
                <ChevronLeft size={16} />
              </button>
              <button
                onClick={goToNextSlide}
                className="p-2 rounded-full bg-slate-800/80 hover:bg-slate-700/80 text-white transition-colors"
                aria-label="Next slide"
              >
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 