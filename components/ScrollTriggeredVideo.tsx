"use client"

import React, { useRef, useEffect, useState } from 'react'
import { motion, useInView, useScroll, useTransform } from 'framer-motion'

interface ScrollTriggeredVideoProps {
  src: string
  title: string
  description: string
  align?: 'left' | 'right'
  darkOverlay?: boolean
  className?: string
}

export default function ScrollTriggeredVideo({
  src,
  title,
  description,
  align = 'left',
  darkOverlay = false,
  className = ''
}: ScrollTriggeredVideoProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: false, amount: 0.3 })
  const [isPlaying, setIsPlaying] = useState(false)
  
  // Scroll-based animation
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })
  
  // Transform values based on scroll
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.8, 1, 1, 0.8])
  
  // Handle video play/pause based on visibility
  useEffect(() => {
    if (!videoRef.current) return
    
    if (isInView) {
      videoRef.current.play().catch(error => {
        // Handle autoplay restrictions
        console.log('Autoplay prevented:', error)
      })
      setIsPlaying(true)
    } else {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }, [isInView])
  
  return (
    <motion.div 
      ref={containerRef}
      style={{ opacity, scale }}
      className={`relative w-full py-16 md:py-24 ${className}`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`flex flex-col ${align === 'right' ? 'md:flex-row-reverse' : 'md:flex-row'} items-center gap-8 md:gap-12`}>
          {/* Video container */}
          <div className="w-full md:w-1/2 relative rounded-xl overflow-hidden shadow-2xl">
            <div className={`absolute inset-0 ${darkOverlay ? 'bg-black/30' : 'bg-gradient-to-br from-blue-500/10 to-purple-500/10'} z-10`}></div>
            
            {/* Play/pause indicator */}
            <div className={`absolute inset-0 flex items-center justify-center z-20 transition-opacity duration-300 ${isPlaying ? 'opacity-0' : 'opacity-100'}`}>
              <div className="bg-white/10 backdrop-blur-sm p-4 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="white" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                  <polygon points="5 3 19 12 5 21 5 3"></polygon>
                </svg>
              </div>
            </div>
            
            {/* Video element */}
            <video 
              ref={videoRef}
              className="w-full h-full object-cover"
              loop
              muted
              playsInline
              preload="metadata"
            >
              <source src={src} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
          </div>
          
          {/* Content */}
          <div className="w-full md:w-1/2 space-y-4">
            <motion.h3 
              className="text-2xl md:text-3xl font-bold gradient-text"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {title}
            </motion.h3>
            <motion.p 
              className="text-slate-300 text-lg"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              {description}
            </motion.p>
          </div>
        </div>
      </div>
    </motion.div>
  )
} 