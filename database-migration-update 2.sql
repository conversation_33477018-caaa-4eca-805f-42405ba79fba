-- Enhanced database migration for SaaS Ideas application
-- Run this in your Supabase SQL Editor

-- First, let's check what tables exist and add missing columns safely

-- Add new columns to existing projects table (if they don't exist)
DO $$
BEGIN
    -- Add generated_tasks column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='generated_tasks') THEN
        ALTER TABLE public.projects ADD COLUMN generated_tasks JSONB;
    END IF;

    -- Add tasks_explanation column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='tasks_explanation') THEN
        ALTER TABLE public.projects ADD COLUMN tasks_explanation TEXT;
    END IF;

    -- Add tasks_generated_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='tasks_generated_at') THEN
        ALTER TABLE public.projects ADD COLUMN tasks_generated_at TIMESTAMPTZ;
    END IF;

    -- Add user_flow_data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='user_flow_data') THEN
        ALTER TABLE public.projects ADD COLUMN user_flow_data JSONB;
    END IF;

    -- Add user_flow_generated_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='user_flow_generated_at') THEN
        ALTER TABLE public.projects ADD COLUMN user_flow_generated_at TIMESTAMPTZ;
    END IF;

    -- Add memory_bank_data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='memory_bank_data') THEN
        ALTER TABLE public.projects ADD COLUMN memory_bank_data JSONB;
    END IF;

    -- Add memory_bank_generated_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='memory_bank_generated_at') THEN
        ALTER TABLE public.projects ADD COLUMN memory_bank_generated_at TIMESTAMPTZ;
    END IF;

    -- Add overview_data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='overview_data') THEN
        ALTER TABLE public.projects ADD COLUMN overview_data JSONB;
    END IF;

    -- Add overview_generated_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='projects' AND column_name='overview_generated_at') THEN
        ALTER TABLE public.projects ADD COLUMN overview_generated_at TIMESTAMPTZ;
    END IF;
END $$;

-- Create tasks table for kanban board (only if it doesn't exist)
CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'done')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  category TEXT,
  estimated_hours INTEGER,
  dependencies JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add foreign key constraint only if tasks table was just created
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'tasks_project_id_fkey'
        AND table_name = 'tasks'
    ) THEN
        ALTER TABLE public.tasks
        ADD CONSTRAINT tasks_project_id_fkey
        FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Handle user_flows table - it might already exist
DO $$
BEGIN
    -- Check if user_flows table exists, if not create it
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_flows') THEN
        CREATE TABLE public.user_flows (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          project_id UUID NOT NULL,
          flow_data JSONB NOT NULL,
          created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
          version INTEGER DEFAULT 1
        );

        -- Add foreign key constraint
        ALTER TABLE public.user_flows
        ADD CONSTRAINT user_flows_project_id_fkey
        FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Create indexes for faster queries (only if they don't exist)
CREATE INDEX IF NOT EXISTS tasks_project_id_idx ON public.tasks (project_id);
CREATE INDEX IF NOT EXISTS tasks_status_idx ON public.tasks (status);

-- Try to create user_flows index only if table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_flows') THEN
        CREATE INDEX IF NOT EXISTS user_flows_project_id_idx ON public.user_flows (project_id);
    END IF;
END $$;

-- Add RLS policies for new tables (only if tables exist)
DO $$
BEGIN
    -- Enable RLS on tasks table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tasks') THEN
        ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enable RLS on user_flows table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_flows') THEN
        ALTER TABLE public.user_flows ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Tasks table policies
-- Drop existing policies if they exist
DROP POLICY IF EXISTS select_own_tasks ON public.tasks;
DROP POLICY IF EXISTS insert_own_tasks ON public.tasks;
DROP POLICY IF EXISTS update_own_tasks ON public.tasks;
DROP POLICY IF EXISTS delete_own_tasks ON public.tasks;

CREATE POLICY select_own_tasks
  ON public.tasks
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = tasks.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY insert_own_tasks
  ON public.tasks
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = tasks.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY update_own_tasks
  ON public.tasks
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = tasks.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY delete_own_tasks
  ON public.tasks
  FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = tasks.project_id
    AND projects.user_id = auth.uid()
  ));

-- User flows table policies
-- Drop existing policies if they exist
DROP POLICY IF EXISTS select_own_user_flows ON public.user_flows;
DROP POLICY IF EXISTS insert_own_user_flows ON public.user_flows;
DROP POLICY IF EXISTS update_own_user_flows ON public.user_flows;
DROP POLICY IF EXISTS delete_own_user_flows ON public.user_flows;

CREATE POLICY select_own_user_flows
  ON public.user_flows
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = user_flows.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY insert_own_user_flows
  ON public.user_flows
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = user_flows.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY update_own_user_flows
  ON public.user_flows
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = user_flows.project_id
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY delete_own_user_flows
  ON public.user_flows
  FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM public.projects
    WHERE projects.id = user_flows.project_id
    AND projects.user_id = auth.uid()
  ));

-- Create trigger for tasks updated_at
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS update_tasks_updated_at ON public.tasks;
DROP TRIGGER IF EXISTS update_user_flows_updated_at ON public.user_flows;

CREATE TRIGGER update_tasks_updated_at
BEFORE UPDATE ON public.tasks
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_user_flows_updated_at
BEFORE UPDATE ON public.user_flows
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
