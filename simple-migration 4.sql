-- Simple migration script for SaaS Ideas application
-- Run this step by step in your Supabase SQL Editor

-- Step 1: Create tasks table without foreign key constraint first
CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'done')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  category TEXT,
  estimated_hours INTEGER,
  dependencies JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Step 2: Add columns to projects table if they don't exist
-- (Run each ALTER TABLE statement separately)

-- Add generated_tasks column
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS generated_tasks JSONB;

-- Add tasks_explanation column  
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS tasks_explanation TEXT;

-- Add tasks_generated_at column
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS tasks_generated_at TIMESTAMPTZ;

-- Add user_flow_data column
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS user_flow_data JSONB;

-- Add user_flow_generated_at column
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS user_flow_generated_at TIMESTAMPTZ;

-- Add memory_bank_data column
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS memory_bank_data JSONB;

-- Add memory_bank_generated_at column
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS memory_bank_generated_at TIMESTAMPTZ;

-- Add overview_data column
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS overview_data JSONB;

-- Add overview_generated_at column
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS overview_generated_at TIMESTAMPTZ;

-- Step 3: Create indexes
CREATE INDEX IF NOT EXISTS tasks_project_id_idx ON public.tasks (project_id);
CREATE INDEX IF NOT EXISTS tasks_status_idx ON public.tasks (status);

-- Step 4: Enable RLS on tasks table
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Step 5: Create RLS policies for tasks table
-- (Drop existing policies first if they exist)
DROP POLICY IF EXISTS select_own_tasks ON public.tasks;
DROP POLICY IF EXISTS insert_own_tasks ON public.tasks;
DROP POLICY IF EXISTS update_own_tasks ON public.tasks;
DROP POLICY IF EXISTS delete_own_tasks ON public.tasks;

-- Create new policies
CREATE POLICY select_own_tasks 
  ON public.tasks 
  FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.projects 
    WHERE projects.id = tasks.project_id 
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY insert_own_tasks 
  ON public.tasks 
  FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.projects 
    WHERE projects.id = tasks.project_id 
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY update_own_tasks 
  ON public.tasks 
  FOR UPDATE 
  USING (EXISTS (
    SELECT 1 FROM public.projects 
    WHERE projects.id = tasks.project_id 
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY delete_own_tasks 
  ON public.tasks 
  FOR DELETE 
  USING (EXISTS (
    SELECT 1 FROM public.projects 
    WHERE projects.id = tasks.project_id 
    AND projects.user_id = auth.uid()
  ));

-- Step 6: Create trigger for tasks updated_at
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_tasks_updated_at ON public.tasks;
CREATE TRIGGER update_tasks_updated_at
BEFORE UPDATE ON public.tasks
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Step 7: Test the setup
-- You can run this to verify everything works:
-- SELECT table_name FROM information_schema.tables WHERE table_name IN ('projects', 'tasks');
-- SELECT column_name FROM information_schema.columns WHERE table_name = 'projects' AND column_name LIKE '%task%';
