"use server";

import { IdeaValidationInput, IdeaValidationResult } from '@/lib/services/idea-validator-service';
import {
  findExistingValidationAction,
  saveValidationResultAction
} from './idea-validation-db-actions';

export async function validateIdeaWithGemini(
  input: IdeaValidationInput,
  useCache: boolean = true
): Promise<IdeaValidationResult> {
  try {
    // Check if we have a cached result for the same input
    if (useCache) {
      console.log('Checking for existing validation...');
      const existingValidation = await findExistingValidationAction(input);

      if (existingValidation) {
        console.log('Found existing validation, returning cached result');
        return {
          ...existingValidation.validation_result,
          validationDate: existingValidation.created_at
        };
      }
    }

    console.log('No cached validation found, generating new analysis...');
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Gemini API key is not set");
    }

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;

    const prompt = `
You are an expert startup advisor and market analyst. Analyze this SaaS idea comprehensively and provide detailed validation insights.

IDEA DETAILS:
- Title: ${input.ideaTitle}
- Description: ${input.ideaDescription}
- Target Market: ${input.targetMarket}
- Business Model: ${input.businessModel}
- Technical Complexity: ${input.technicalComplexity || 'medium'}
- Budget: ${input.budget ? `$${input.budget}` : 'Not specified'}
- Timeline: ${input.timeline || 'Not specified'}

ANALYSIS REQUIREMENTS:
Provide a comprehensive analysis in the following JSON format. Be realistic, data-driven, and actionable.

{
  "overallScore": [0-100 overall viability score],
  "marketPotential": {
    "score": [0-100],
    "marketSize": "[estimated market size with reasoning]",
    "competitorCount": "[number and description of competitors]",
    "demandLevel": "[low/medium/high]",
    "insights": ["insight 1", "insight 2", "insight 3"]
  },
  "technicalFeasibility": {
    "score": [0-100],
    "complexity": "[low/medium/high]",
    "requiredSkills": ["skill 1", "skill 2", "skill 3"],
    "estimatedDevelopmentTime": "[realistic timeline]",
    "technicalRisks": ["risk 1", "risk 2", "risk 3"]
  },
  "businessViability": {
    "score": [0-100],
    "revenueModel": ["model 1", "model 2"],
    "estimatedRevenue": "[realistic revenue projections]",
    "breakEvenTime": "[estimated time to break even]",
    "fundingRequired": "[estimated funding needed]",
    "businessRisks": ["risk 1", "risk 2", "risk 3"]
  },
  "competitiveAnalysis": {
    "score": [0-100],
    "mainCompetitors": ["competitor 1", "competitor 2", "competitor 3"],
    "competitiveAdvantage": ["advantage 1", "advantage 2"],
    "marketGap": "[description of market gap]",
    "differentiationStrategy": ["strategy 1", "strategy 2"]
  },
  "recommendations": {
    "strengths": ["strength 1", "strength 2", "strength 3"],
    "weaknesses": ["weakness 1", "weakness 2", "weakness 3"],
    "opportunities": ["opportunity 1", "opportunity 2", "opportunity 3"],
    "threats": ["threat 1", "threat 2", "threat 3"],
    "nextSteps": ["step 1", "step 2", "step 3", "step 4", "step 5"],
    "pivotSuggestions": ["suggestion 1", "suggestion 2"]
  },
  "successProbability": [0-100],
  "riskLevel": "[low/medium/high]"
}

IMPORTANT: 
- Be honest and realistic in your assessment
- Consider current market trends and competition
- Factor in the technical complexity and required resources
- Provide actionable insights and specific recommendations
- Base scores on realistic market data and startup success rates
- Consider the target market size and accessibility
- Evaluate the business model sustainability
`;

    // Make the API request to Gemini
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error (${response.status}): ${errorText}`);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content) {
      throw new Error("No response content received from Gemini API");
    }

    // Extract the text content from the response
    const textContent = data.candidates[0].content.parts[0].text;

    // Extract JSON from the response
    const jsonMatch = textContent.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('Failed to parse AI response')
    }

    const validationData = JSON.parse(jsonMatch[0])

    const result: IdeaValidationResult = {
      ...validationData,
      validationDate: new Date().toISOString()
    }

    // Save the result to database if caching is enabled
    if (useCache) {
      try {
        const saveResult = await saveValidationResultAction(input, result);
        if (saveResult.success) {
          console.log('Validation result saved to database');
        } else {
          console.error('Failed to save validation result:', saveResult.error);
        }
      } catch (saveError) {
        console.error('Failed to save validation result:', saveError);
        // Don't throw error here, just log it - we still want to return the result
      }
    }

    return result
  } catch (error) {
    console.error('Error validating idea with Gemini:', error)

    // Fallback validation result
    const fallbackResult: IdeaValidationResult = {
      overallScore: 65,
      marketPotential: {
        score: 70,
        marketSize: "Medium-sized market with growth potential",
        competitorCount: "Several established competitors",
        demandLevel: 'medium' as const,
        insights: [
          "Market shows steady growth trends",
          "Customer pain points are well-defined",
          "Opportunity for differentiation exists"
        ]
      },
      technicalFeasibility: {
        score: 75,
        complexity: input.technicalComplexity || 'medium' as const,
        requiredSkills: ["Full-stack development", "UI/UX design", "Database management"],
        estimatedDevelopmentTime: "3-6 months for MVP",
        technicalRisks: [
          "Scalability challenges",
          "Integration complexity",
          "Security considerations"
        ]
      },
      businessViability: {
        score: 60,
        revenueModel: ["Subscription-based", "Freemium"],
        estimatedRevenue: "$10K-50K MRR within 12 months",
        breakEvenTime: "8-12 months",
        fundingRequired: "$50K-100K",
        businessRisks: [
          "Customer acquisition cost",
          "Market competition",
          "Revenue model validation"
        ]
      },
      competitiveAnalysis: {
        score: 55,
        mainCompetitors: ["Established SaaS platforms", "Enterprise solutions"],
        competitiveAdvantage: ["Better user experience", "Focused feature set"],
        marketGap: "Simplified solution for specific use case",
        differentiationStrategy: ["Niche targeting", "Superior UX"]
      },
      recommendations: {
        strengths: [
          "Clear problem identification",
          "Defined target market",
          "Feasible technical approach"
        ],
        weaknesses: [
          "Competitive market",
          "Customer acquisition challenges",
          "Revenue model uncertainty"
        ],
        opportunities: [
          "Market growth potential",
          "Technology advancement",
          "Partnership possibilities"
        ],
        threats: [
          "Established competition",
          "Market saturation",
          "Economic factors"
        ],
        nextSteps: [
          "Conduct customer interviews",
          "Build MVP prototype",
          "Validate pricing model",
          "Analyze competitor features",
          "Test market demand"
        ]
      },
      successProbability: 65,
      riskLevel: 'medium' as const,
      validationDate: new Date().toISOString()
    }

    // Try to save fallback result to database if caching is enabled
    if (useCache) {
      try {
        const saveResult = await saveValidationResultAction(input, fallbackResult);
        if (saveResult.success) {
          console.log('Fallback validation result saved to database');
        } else {
          console.error('Failed to save fallback validation result:', saveResult.error);
        }
      } catch (saveError) {
        console.error('Failed to save fallback validation result:', saveError);
      }
    }

    return fallbackResult
  }
}
