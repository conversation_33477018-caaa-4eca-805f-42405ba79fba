import { createClient } from '@/lib/supabase/client'
import { IdeaValidationInput, IdeaValidationResult } from './idea-validator-service'
import { generateInputHash } from '@/lib/utils/validation-hash'

export interface IdeaValidationRecord {
  id: string
  user_id: string
  idea_title: string
  idea_description: string
  target_market: string
  business_model: string
  technical_complexity: string
  budget?: number
  timeline?: string
  input_hash: string
  validation_result: IdeaValidationResult
  created_at: string
  updated_at: string
}

// Re-export the hash function for convenience
export { generateInputHash } from '@/lib/utils/validation-hash'

// Check if validation exists for the same input
export async function findExistingValidation(
  input: IdeaValidationInput, 
  userId: string
): Promise<IdeaValidationRecord | null> {
  try {
    const supabase = createClient()
    const inputHash = generateInputHash(input)
    
    const { data, error } = await supabase
      .from('idea_validations')
      .select('*')
      .eq('user_id', userId)
      .eq('input_hash', inputHash)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle()
    
    if (error) {
      console.error('Error finding existing validation:', error)
      return null
    }
    
    return data
  } catch (error) {
    console.error('Error in findExistingValidation:', error)
    return null
  }
}

// Save validation result to database
export async function saveValidationResult(
  input: IdeaValidationInput,
  result: IdeaValidationResult,
  userId: string
): Promise<IdeaValidationRecord | null> {
  try {
    const supabase = createClient()
    const inputHash = generateInputHash(input)
    
    const validationRecord = {
      user_id: userId,
      idea_title: input.ideaTitle,
      idea_description: input.ideaDescription,
      target_market: input.targetMarket,
      business_model: input.businessModel,
      technical_complexity: input.technicalComplexity || 'medium',
      budget: input.budget,
      timeline: input.timeline,
      input_hash: inputHash,
      validation_result: result
    }
    
    const { data, error } = await supabase
      .from('idea_validations')
      .insert(validationRecord)
      .select()
      .single()
    
    if (error) {
      console.error('Error saving validation result:', error)
      throw new Error(`Failed to save validation: ${error.message}`)
    }
    
    return data
  } catch (error) {
    console.error('Error in saveValidationResult:', error)
    throw error
  }
}

// Get all validations for a user
export async function getUserValidations(userId: string): Promise<IdeaValidationRecord[]> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('idea_validations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Error getting user validations:', error)
      throw new Error(`Failed to get validations: ${error.message}`)
    }
    
    return data || []
  } catch (error) {
    console.error('Error in getUserValidations:', error)
    throw error
  }
}

// Get validation by ID
export async function getValidationById(
  id: string, 
  userId: string
): Promise<IdeaValidationRecord | null> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('idea_validations')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single()
    
    if (error) {
      console.error('Error getting validation by ID:', error)
      return null
    }
    
    return data
  } catch (error) {
    console.error('Error in getValidationById:', error)
    return null
  }
}

// Delete validation
export async function deleteValidation(id: string, userId: string): Promise<boolean> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('idea_validations')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error deleting validation:', error)
      throw new Error(`Failed to delete validation: ${error.message}`)
    }
    
    return true
  } catch (error) {
    console.error('Error in deleteValidation:', error)
    throw error
  }
}

// Update validation result
export async function updateValidationResult(
  id: string,
  result: IdeaValidationResult,
  userId: string
): Promise<IdeaValidationRecord | null> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('idea_validations')
      .update({
        validation_result: result,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating validation result:', error)
      throw new Error(`Failed to update validation: ${error.message}`)
    }
    
    return data
  } catch (error) {
    console.error('Error in updateValidationResult:', error)
    throw error
  }
}
