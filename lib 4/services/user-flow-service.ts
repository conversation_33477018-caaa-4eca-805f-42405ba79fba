"use client"

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for client-side operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export interface UserFlowStep {
  id: string;
  title: string;
  icon: string;
  points: string[];
  position: { x: number; y: number };
  connections: string[];
}

// Save user flow data
export async function saveUserFlow(projectName: string, flowData: UserFlowStep[]) {
  try {
    console.log(`Saving user flow for project "${projectName}"`);
    
    // Insert or update the user flow in the database
    const { data, error } = await supabase
      .from('user_flows')
      .upsert({
        project_name: projectName,
        flow_data: flowData,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'project_name'
      })
      .select('id')
      .single();
    
    if (error) {
      console.error('Error saving user flow:', error);
      throw new Error(`Failed to save user flow: ${error.message}`);
    }
    
    console.log(`User flow saved successfully for project: ${projectName}`);
    return data;
  } catch (error) {
    console.error('Error in saveUserFlow:', error);
    throw error;
  }
}

// Get user flow data
export async function getUserFlow(projectName: string): Promise<UserFlowStep[] | null> {
  try {
    const { data, error } = await supabase
      .from('user_flows')
      .select('flow_data')
      .eq('project_name', projectName)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No data found, return null
        return null;
      }
      throw error;
    }
    
    return data?.flow_data || null;
  } catch (error) {
    console.error('Error getting user flow:', error);
    return null; // Return null instead of throwing to allow fallback
  }
}