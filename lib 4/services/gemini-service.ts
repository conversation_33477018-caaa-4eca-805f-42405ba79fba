"use server"

import { AIAnalysisResult } from '@/components/studio/ai-analysis/ai-analysis-service';

export async function analyzeProjectWithGemini(input: {
  name: string;
  description: string;
}): Promise<AIAnalysisResult> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Gemini API key is not set");
    }
    
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;
    
    console.log("Preparing Gemini API request...");
    
    // Create a prompt for the Gemini API
    const prompt = `
      Analyze the following SaaS project idea:
      
      Name: ${input.name}
      Description: ${input.description}
      
      Provide a comprehensive analysis based on these six core pillars:
      1. Uniqueness (how different is it from existing solutions)
      2. Stickiness (potential for user retention)
      3. Growth Potential (market growth trajectory)
      4. Pricing Model (pricing strategy potential)
      5. Upsell Potential (opportunities for additional revenue)
      6. Customer Purchasing Power (target audience's ability to pay)
      
      Also provide:
      - Overall feasibility score
      - Suggested improvements
      - Core features with priorities
      - Technical requirements (frontend, backend, database, infrastructure)
      - Pricing model suggestions

      IMPORTANT PRICING GUIDELINES:
      - Generate realistic SaaS pricing (typically $0-$500/month for most tiers)
      - Use common pricing patterns: Free ($0), Starter ($9-29), Pro ($49-99), Enterprise ($199-499)
      - Avoid unrealistic prices like $100250500 or other nonsensical amounts
      - Consider the target market and typical SaaS pricing standards
      - Price should reflect the value proposition and market positioning

      Format the response as a structured JSON object with these exact fields:
      {
        "projectName": string,
        "projectDescription": string,
        "marketFeasibility": {
          "pillars": [
            { "name": string, "score": number, "description": string }
          ],
          "overallScore": number
        },
        "suggestedImprovements": string[],
        "coreFeatures": [
          { "name": string, "description": string, "priority": "high" | "medium" | "low" }
        ],
        "technicalRequirements": {
          "frontend": string[],
          "backend": string[],
          "database": string[],
          "infrastructure": string[]
        },
        "pricingModel": [
          {
            "name": string,
            "price": number (realistic monthly price in USD, e.g., 0, 19, 49, 99, 299),
            "features": string[],
            "recommended": boolean
          }
        ]
      }
    `;
    
    // Make the API request to Gemini
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7
        }
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error (${response.status}): ${errorText}`);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content) {
      throw new Error("No response content received from Gemini API");
    }
    
    // Extract the text content from the response
    const textContent = data.candidates[0].content.parts[0].text;
    
    // Find the JSON object in the text content
    const jsonMatch = textContent.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("Could not extract JSON from Gemini response");
    }
    
    // Parse the JSON response
    const result = JSON.parse(jsonMatch[0]);
    
    return result as AIAnalysisResult;
  } catch (error) {
    console.error('Error in analyzeProjectWithGemini:', error);
    throw error;
  }
}

export async function generateUserFlowWithGemini(input: {
  name: string;
  description: string;
  features: Array<{ name: string; description: string; priority: string }>;
}): Promise<Array<{
  id: string;
  title: string;
  icon: string;
  points: string[];
  position: { x: number; y: number };
  connections: string[];
}>> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Gemini API key is not set");
    }
    
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;
    
    console.log("Generating user flow with Gemini...");
    
    // Create a prompt for generating user flow
    const prompt = `
      Generate a user flow diagram for this SaaS project from the user's perspective:
      
      Project Name: ${input.name}
      Description: ${input.description}
      Core Features: ${input.features.map(f => f.name).join(', ')}
      
      Create a user flow that shows how users will interact with the website/application.
      Each step should represent a key page or interaction point in the user journey.
      
      Generate 5-6 flow steps that include:
      1. Landing Page (entry point)
      2. User Authentication (signup/login)
      3. Main Dashboard/Hub
      4. Core Feature Pages (based on the project features)
      5. Additional functionality (collaboration, settings, etc.)
      6. Export/Integration features
      
      For each step, provide:
      - A descriptive title (ALL CAPS)
      - An appropriate emoji icon
      - Exactly 4 key points that describe what this step contains
      - Position coordinates for horizontal layout (IMPORTANT: Each card is 320px wide and 280px tall, use horizontal flow with proper spacing)
      - Connections to other steps
      
      POSITIONING GUIDELINES FOR HORIZONTAL FLOW:
      - Use a horizontal flow layout optimized for scrolling
      - Cards are 320px wide and 280px tall
      - Minimum 150px horizontal spacing between cards
      - Minimum 100px vertical spacing between rows
      - Start positions: x:60, y:80 for top-left (space for arrows above)
      - Layout: Maximum 2 rows, horizontal flow
      - Example positions for 6 cards: (60,80), (530,80), (1000,80), (60,460), (530,460), (1000,460)
      
      Format the response as a JSON array with this exact structure:
      [
        {
          "id": "unique_id",
          "title": "STEP TITLE",
          "icon": "📱",
          "points": [
            "First key feature or element",
            "Second key feature or element",
            "Third key feature or element",
            "Fourth key feature or element"
          ],
          "position": { "x": 60, "y": 80 },
          "connections": ["next_step_id"]
        }
      ]
      
      Make sure the flow represents a logical user journey and the positions create a well-spaced visual layout without overlapping.
    `;
    
    // Make the API request to Gemini
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7
        }
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error (${response.status}): ${errorText}`);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content) {
      throw new Error("No response content received from Gemini API");
    }
    
    // Extract the text content from the response
    const textContent = data.candidates[0].content.parts[0].text;
    
    // Find the JSON array in the text content
    const jsonMatch = textContent.match(/\[[\s\S]*\]/);
    if (!jsonMatch) {
      throw new Error("Could not extract JSON from Gemini response");
    }
    
    // Parse the JSON response
    const result = JSON.parse(jsonMatch[0]);
    
    return result;
  } catch (error) {
    console.error('Error in generateUserFlowWithGemini:', error);
    throw error;
  }
}

export async function generateFlowExplanationWithGemini(input: {
  name: string;
  description: string;
  features: Array<{ name: string; description: string; priority: string }>;
  flowSteps: Array<{
    id: string;
    title: string;
    icon: string;
    points: string[];
    position: { x: number; y: number };
    connections: string[];
  }>;
}): Promise<string> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Gemini API key is not set");
    }

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;

    console.log("Generating flow explanation with Gemini...");

    // Create a prompt for generating flow explanation
    const prompt = `
      Generate a comprehensive explanation for this user flow diagram:

      Project Name: ${input.name}
      Description: ${input.description}
      Core Features: ${input.features.map(f => f.name).join(', ')}

      Flow Steps:
      ${input.flowSteps.map(step => `
      - ${step.title}: ${step.points.join(', ')}
      `).join('')}

      Write a detailed explanation that covers:
      1. What this user flow represents
      2. The main user journey and key interactions
      3. How each step connects to create a seamless experience
      4. Key components and their purposes

      Keep the explanation informative but accessible, around 2-3 paragraphs.
      Focus on the user experience and business value.

      Return only the explanation text, no additional formatting or JSON.
    `;

    // Make the API request to Gemini
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error (${response.status}): ${errorText}`);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content) {
      throw new Error("No response content received from Gemini API");
    }

    // Extract the text content from the response
    const explanation = data.candidates[0].content.parts[0].text.trim();

    return explanation;
  } catch (error) {
    console.error('Error in generateFlowExplanationWithGemini:', error);
    // Return a fallback explanation
    return `This user flow diagram represents the core user journey through ${input.name} based on the AI analysis of your project idea. The diagram shows the main screens and interactions that users will experience, from initial landing to core feature usage. Each step is designed to guide users through a logical progression that maximizes engagement and feature adoption.`;
  }
}

export async function generateTasksWithGemini(input: {
  name: string;
  description: string;
  features: Array<{ name: string; description: string; priority: string }>;
  technicalRequirements: {
    frontend: string[];
    backend: string[];
    database: string[];
    infrastructure: string[];
  };
}): Promise<Array<{
  id: string;
  title: string;
  description: string;
  status: "todo" | "in_progress" | "done";
  priority: "high" | "medium" | "low";
  category: string;
  estimatedHours?: number;
  dependencies?: string[];
}>> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Gemini API key is not set");
    }

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;

    console.log("Generating tasks with Gemini...");

    // Create a prompt for generating tasks
    const prompt = `
      Generate a comprehensive task list for this SaaS project development:

      Project Name: ${input.name}
      Description: ${input.description}

      Core Features:
      ${input.features.map(f => `- ${f.name} (${f.priority} priority): ${f.description}`).join('\n')}

      Technical Requirements:
      Frontend: ${input.technicalRequirements.frontend.join(', ')}
      Backend: ${input.technicalRequirements.backend.join(', ')}
      Database: ${input.technicalRequirements.database.join(', ')}
      Infrastructure: ${input.technicalRequirements.infrastructure.join(', ')}

      Generate a realistic development task list that includes:

      1. **Setup & Planning Tasks** (2-3 tasks)
      2. **Design Tasks** (1-2 tasks per major feature)
      3. **Development Tasks** (2-3 tasks per feature - frontend, backend, integration)
      4. **Testing Tasks** (1 task per feature)
      5. **DevOps & Deployment Tasks** (3-4 tasks)
      6. **Documentation & Polish Tasks** (2-3 tasks)

      For each task, provide:
      - Unique ID
      - Clear, actionable title
      - Detailed description explaining what needs to be done
      - Realistic status (most should be "todo", some early setup tasks can be "done", 1-2 can be "in_progress")
      - Priority based on project needs and dependencies
      - Category (Setup, Design, Frontend, Backend, Testing, DevOps, Documentation)
      - Estimated hours (realistic development time)
      - Dependencies (if applicable)

      Generate 15-25 tasks total. Make them specific to this project and technically accurate.

      Format the response as a JSON array with this exact structure:
      [
        {
          "id": "unique_task_id",
          "title": "Task Title",
          "description": "Detailed description of what needs to be accomplished",
          "status": "todo" | "in_progress" | "done",
          "priority": "high" | "medium" | "low",
          "category": "Setup" | "Design" | "Frontend" | "Backend" | "Testing" | "DevOps" | "Documentation",
          "estimatedHours": number,
          "dependencies": ["other_task_id"] (optional)
        }
      ]

      Make sure the tasks are realistic, well-organized, and follow a logical development sequence.
    `;

    // Make the API request to Gemini
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error (${response.status}): ${errorText}`);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content) {
      throw new Error("No response content received from Gemini API");
    }

    // Extract the text content from the response
    const textContent = data.candidates[0].content.parts[0].text;

    // Find the JSON array in the text content
    const jsonMatch = textContent.match(/\[[\s\S]*\]/);
    if (!jsonMatch) {
      throw new Error("Could not extract JSON from Gemini response");
    }

    // Parse the JSON response
    const result = JSON.parse(jsonMatch[0]);

    return result;
  } catch (error) {
    console.error('Error in generateTasksWithGemini:', error);
    throw error;
  }
}

export async function generateTasksExplanationWithGemini(input: {
  name: string;
  description: string;
  features: Array<{ name: string; description: string; priority: string }>;
  technicalRequirements: {
    frontend: string[];
    backend: string[];
    database: string[];
    infrastructure: string[];
  };
  generatedTasks: Array<{
    id: string;
    title: string;
    description: string;
    status: string;
    priority: string;
    category: string;
    estimatedHours?: number;
  }>;
}): Promise<string> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Gemini API key is not set");
    }

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;

    console.log("Generating tasks explanation with Gemini...");

    // Get task statistics
    const taskStats = {
      total: input.generatedTasks.length,
      byCategory: input.generatedTasks.reduce((acc, task) => {
        acc[task.category] = (acc[task.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      byPriority: input.generatedTasks.reduce((acc, task) => {
        acc[task.priority] = (acc[task.priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      totalHours: input.generatedTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0)
    };

    // Create a prompt for generating tasks explanation
    const prompt = `
      Generate a comprehensive "About Tasks" explanation for this AI-generated task list:

      Project: ${input.name}
      Description: ${input.description}

      Core Features: ${input.features.map(f => f.name).join(', ')}

      Technical Stack:
      - Frontend: ${input.technicalRequirements.frontend.join(', ')}
      - Backend: ${input.technicalRequirements.backend.join(', ')}
      - Database: ${input.technicalRequirements.database.join(', ')}
      - Infrastructure: ${input.technicalRequirements.infrastructure.join(', ')}

      Generated Task Statistics:
      - Total Tasks: ${taskStats.total}
      - Categories: ${Object.entries(taskStats.byCategory).map(([cat, count]) => `${cat} (${count})`).join(', ')}
      - Priority Distribution: ${Object.entries(taskStats.byPriority).map(([pri, count]) => `${pri} (${count})`).join(', ')}
      - Estimated Total Hours: ${taskStats.totalHours}

      Sample Tasks:
      ${input.generatedTasks.slice(0, 5).map(task => `- ${task.title} (${task.category}, ${task.priority} priority)`).join('\n')}

      Write a brief explanation (2-3 sentences) covering:

      1. How AI analyzed ${input.name} to generate these tasks
      2. The task organization approach used
      3. Brief note about customization possibilities

      Keep it concise, professional, and focused on the AI intelligence behind task generation.

      Return only the explanation text, no additional formatting or JSON.
    `;

    // Make the API request to Gemini
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error (${response.status}): ${errorText}`);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content) {
      throw new Error("No response content received from Gemini API");
    }

    // Extract the text content from the response
    const explanation = data.candidates[0].content.parts[0].text.trim();

    return explanation;
  } catch (error) {
    console.error('Error in generateTasksExplanationWithGemini:', error);
    // Return a fallback explanation
    return `These ${input.generatedTasks.length} tasks have been intelligently generated by AI based on a comprehensive analysis of ${input.name}. The AI considered your project's core features, technical requirements, and development best practices to create a realistic roadmap. Tasks are organized across ${Object.keys(input.generatedTasks.reduce((acc, task) => { acc[task.category] = true; return acc; }, {} as Record<string, boolean>)).length} categories with priority-based scheduling to optimize your development workflow. You can filter, regenerate, or customize these tasks to match your specific development approach and timeline.`;
  }
}

export async function generateOverviewWithGemini(input: {
  name: string;
  description: string;
  features: Array<{ name: string; description: string; priority: string }>;
  marketFeasibility: {
    pillars: Array<{ name: string; score: number; description: string }>;
    overallScore: number;
  };
  technicalRequirements: {
    frontend: string[];
    backend: string[];
    database: string[];
    infrastructure: string[];
  };
}): Promise<{
  summary: string;
  keyMetrics: Array<{ label: string; value: string; trend: 'up' | 'down' | 'stable' }>;
  recommendations: string[];
  nextSteps: string[];
}> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Gemini API key is not set");
    }

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;

    console.log("Generating overview with Gemini...");

    const prompt = `
      Generate a comprehensive project overview for this SaaS project:

      Project Name: ${input.name}
      Description: ${input.description}

      Core Features:
      ${input.features.map(f => `- ${f.name} (${f.priority} priority): ${f.description}`).join('\n')}

      Market Feasibility Analysis:
      Overall Score: ${input.marketFeasibility.overallScore}/100
      ${input.marketFeasibility.pillars.map(p => `- ${p.name}: ${p.score}/100 - ${p.description}`).join('\n')}

      Technical Stack:
      Frontend: ${input.technicalRequirements.frontend.join(', ')}
      Backend: ${input.technicalRequirements.backend.join(', ')}
      Database: ${input.technicalRequirements.database.join(', ')}
      Infrastructure: ${input.technicalRequirements.infrastructure.join(', ')}

      Generate a project overview that includes:

      1. **Summary**: A concise 1-2 sentence overview of the project's potential and key value proposition
      2. **Key Metrics**: 4-5 important metrics with realistic values and trends
      3. **Recommendations**: 3-4 brief strategic recommendations (max 10 words each)
      4. **Next Steps**: 3-4 immediate actionable next steps (max 8 words each)

      Format the response as a JSON object with this exact structure:
      {
        "summary": "Detailed project summary...",
        "keyMetrics": [
          {
            "label": "Metric Name",
            "value": "Value with unit",
            "trend": "up" | "down" | "stable"
          }
        ],
        "recommendations": [
          "Strategic recommendation 1",
          "Strategic recommendation 2"
        ],
        "nextSteps": [
          "Actionable next step 1",
          "Actionable next step 2"
        ]
      }

      Make the content specific to ${input.name} and realistic based on the analysis.
    `;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error (${response.status}): ${errorText}`);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content) {
      throw new Error("No response content received from Gemini API");
    }

    const textContent = data.candidates[0].content.parts[0].text;
    const jsonMatch = textContent.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("Could not extract JSON from Gemini response");
    }

    const result = JSON.parse(jsonMatch[0]);
    return result;
  } catch (error) {
    console.error('Error in generateOverviewWithGemini:', error);
    throw error;
  }
}

export async function generateMemoryBankWithGemini(input: {
  name: string;
  description: string;
  features: Array<{ name: string; description: string; priority: string }>;
  technicalRequirements: {
    frontend: string[];
    backend: string[];
    database: string[];
    infrastructure: string[];
  };
}): Promise<{
  insights: Array<{
    id: string;
    title: string;
    content: string;
    category: 'technical' | 'business' | 'user-experience' | 'market';
    importance: 'high' | 'medium' | 'low';
    tags: string[];
    createdAt: string;
  }>;
  summary: string;
}> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Gemini API key is not set");
    }

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;

    console.log("Generating memory bank with Gemini...");

    const prompt = `
      Generate a comprehensive memory bank of insights for this SaaS project:

      Project Name: ${input.name}
      Description: ${input.description}

      Core Features:
      ${input.features.map(f => `- ${f.name} (${f.priority} priority): ${f.description}`).join('\n')}

      Technical Requirements:
      Frontend: ${input.technicalRequirements.frontend.join(', ')}
      Backend: ${input.technicalRequirements.backend.join(', ')}
      Database: ${input.technicalRequirements.database.join(', ')}
      Infrastructure: ${input.technicalRequirements.infrastructure.join(', ')}

      Generate 6-8 concise key insights across these categories:

      1. **Technical Insights** (2-3 insights): Key architecture decisions, technology choices
      2. **Business Insights** (2 insights): Market opportunities, monetization strategies
      3. **User Experience Insights** (1-2 insights): UX best practices, user behavior
      4. **Market Insights** (1-2 insights): Target audience, positioning strategies

      For each insight, provide:
      - Unique ID
      - Clear, actionable title (max 6 words)
      - Brief content explaining the insight (max 2 sentences)
      - Category classification
      - Importance level
      - 2-3 relevant tags
      - Current timestamp

      Also provide a brief summary (max 1 sentence) explaining the memory bank purpose.

      Format the response as a JSON object with this exact structure:
      {
        "insights": [
          {
            "id": "unique_insight_id",
            "title": "Insight Title",
            "content": "Detailed explanation of the insight and its implications for the project",
            "category": "technical" | "business" | "user-experience" | "market",
            "importance": "high" | "medium" | "low",
            "tags": ["tag1", "tag2", "tag3"],
            "createdAt": "2024-01-15T10:30:00Z"
          }
        ],
        "summary": "Explanation of what this memory bank contains and how to use it effectively"
      }

      Make the insights specific to ${input.name} and actionable for the development team.
    `;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error (${response.status}): ${errorText}`);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content) {
      throw new Error("No response content received from Gemini API");
    }

    const textContent = data.candidates[0].content.parts[0].text;
    const jsonMatch = textContent.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("Could not extract JSON from Gemini response");
    }

    const result = JSON.parse(jsonMatch[0]);
    return result;
  } catch (error) {
    console.error('Error in generateMemoryBankWithGemini:', error);
    throw error;
  }
}