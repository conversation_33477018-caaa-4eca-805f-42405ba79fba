"use client"

import { createClient } from "@/lib/supabase/client"
import { AIAnalysisResult } from "@/components/studio/ai-analysis/ai-analysis-service"

export interface DashboardMetrics {
  totalProjects: number
  completionRate: number
  activeTasks: number
  timeSaved: number
  projectsThisMonth: number
  completionTrend: number
  tasksToday: number
}

export interface DashboardProject {
  id: string
  name: string
  description: string
  progress: number
  lastUpdated: string
  tags: string[]
  status: string
  createdAt: string
}

export interface ActivityItem {
  id: string
  type: 'project' | 'ticket' | 'validation' | 'comment'
  user: {
    name: string
    avatar?: string
    initials: string
  }
  project?: string
  action: string
  target?: string
  time: string
  timestamp: Date
}

// Get dashboard metrics for the current user
export async function getDashboardMetrics(userId: string): Promise<DashboardMetrics> {
  try {
    const supabase = createClient()

    console.log('Fetching dashboard metrics for user:', userId)

    // Get total projects
    const { count: totalProjects, error: projectsError } = await supabase
      .from('projects')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    if (projectsError) {
      console.error('Error fetching total projects:', projectsError)
    }

    // Get projects created this month
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    const { count: projectsThisMonth, error: monthlyError } = await supabase
      .from('projects')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .gte('created_at', startOfMonth.toISOString())

    if (monthlyError) {
      console.error('Error fetching monthly projects:', monthlyError)
    }

    // Get user's project IDs first
    const { data: userProjects } = await supabase
      .from('projects')
      .select('id')
      .eq('user_id', userId)

    const projectIds = userProjects?.map(p => p.id) || []

    console.log('Found project IDs:', projectIds)
    console.log('Total projects count:', totalProjects)
    console.log('Projects this month count:', projectsThisMonth)

    // Get tickets for completion rate and active tasks
    let tickets: any[] = []
    let tasksToday = 0

    if (projectIds.length > 0) {
      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select('status, project_id, created_at')
        .in('project_id', projectIds)

      if (tasksError) {
        console.error('Error fetching tasks:', tasksError)
        // If tasks table doesn't exist, continue with empty array
        tickets = []
      } else {
        tickets = tasksData || []
      }

      // Get tasks due today (simplified - using created today as proxy)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      const { count: tasksTodayCount, error: todayTasksError } = await supabase
        .from('tasks')
        .select('*', { count: 'exact', head: true })
        .in('project_id', projectIds)
        .gte('created_at', today.toISOString())
        .lt('created_at', tomorrow.toISOString())

      if (todayTasksError) {
        console.error('Error fetching today tasks:', todayTasksError)
        tasksToday = 0
      } else {
        tasksToday = tasksTodayCount || 0
      }
    }

    const totalTasks = tickets.length
    const completedTasks = tickets.filter(t => t.status === 'done').length
    const activeTasks = tickets.filter(t => t.status === 'in-progress').length

    // Ensure no NaN values
    const safeCompletionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    const safeTotalProjects = Math.max(0, totalProjects || 0)
    const safeProjectsThisMonth = Math.max(0, projectsThisMonth || 0)
    const safeActiveTasks = Math.max(0, activeTasks)
    const safeTasksToday = Math.max(0, tasksToday)

    // Calculate time saved (simplified metric based on AI features usage)
    const timeSaved = Math.min(safeTotalProjects * 3.5, 100) // 3.5 hours per project, max 100

    return {
      totalProjects: safeTotalProjects,
      completionRate: safeCompletionRate,
      activeTasks: safeActiveTasks,
      timeSaved: Math.round(timeSaved),
      projectsThisMonth: safeProjectsThisMonth,
      completionTrend: safeProjectsThisMonth > 0 ? 5 : 0, // Show trend only if there are projects
      tasksToday: safeTasksToday
    }
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error)
    // Return safe default values
    return {
      totalProjects: 0,
      completionRate: 0,
      activeTasks: 0,
      timeSaved: 0,
      projectsThisMonth: 0,
      completionTrend: 0,
      tasksToday: 0
    }
  }
}

// Get user's projects for dashboard
export async function getDashboardProjects(userId: string): Promise<DashboardProject[]> {
  try {
    const supabase = createClient()

    console.log('Fetching dashboard projects for user:', userId)

    const { data: projects, error } = await supabase
      .from('projects')
      .select(`
        id,
        project_name,
        project_description,
        created_at,
        updated_at,
        progress,
        market_feasibility,
        technical_requirements,
        pricing_model
      `)
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
      .limit(10)

    if (error) {
      console.error('Error fetching projects:', error)
      throw error
    }

    console.log('Found projects:', projects?.length || 0)
    console.log('Raw projects data:', projects)

    const mappedProjects = projects?.map(project => {
      // Calculate progress based on project data
      let progress = project.progress || 25 // Use stored progress or default

      // Extract tags from technical requirements and pricing model
      const tags: string[] = []

      // Extract from technical requirements
      if (project.technical_requirements) {
        const techReq = project.technical_requirements as any
        if (techReq.frontend && Array.isArray(techReq.frontend)) {
          tags.push(...techReq.frontend.slice(0, 2))
        }
        if (techReq.backend && Array.isArray(techReq.backend)) {
          tags.push(...techReq.backend.slice(0, 1))
        }
      }

      // Extract from pricing model
      if (project.pricing_model && Array.isArray(project.pricing_model)) {
        const pricingTags = project.pricing_model.map((p: any) => p.name).slice(0, 1)
        tags.push(...pricingTags)
      }

      // If no tags found, add a default tag based on project type or status
      if (tags.length === 0) {
        tags.push('new') // Default tag for projects without specific tags
      }

      const dashboardProject = {
        id: project.id,
        name: project.project_name || 'Untitled Project',
        description: project.project_description || 'No description available',
        progress,
        lastUpdated: new Date(project.updated_at).toLocaleDateString(),
        tags: Array.isArray(tags) ? tags.slice(0, 3) : ['new'], // Ensure it's always an array
        status: 'active',
        createdAt: project.created_at
      }

      console.log('Mapped dashboard project:', {
        ...dashboardProject,
        tagsType: typeof dashboardProject.tags,
        tagsIsArray: Array.isArray(dashboardProject.tags),
        tagsLength: dashboardProject.tags?.length
      })
      return dashboardProject
    }) || []

    console.log('Final dashboard projects result:', mappedProjects.length, 'projects')
    return mappedProjects
  } catch (error) {
    console.error('Error fetching dashboard projects:', error)
    return []
  }
}

// Get recent activity for dashboard
export async function getDashboardActivity(userId: string): Promise<ActivityItem[]> {
  try {
    const supabase = createClient()
    const activities: ActivityItem[] = []

    // Get recent projects
    const { data: recentProjects } = await supabase
      .from('projects')
      .select('id, project_name, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(3)

    recentProjects?.forEach(project => {
      activities.push({
        id: `project-${project.id}`,
        type: 'project',
        user: {
          name: 'You',
          initials: 'YU'
        },
        project: project.project_name || 'Untitled Project',
        action: 'created project',
        time: getRelativeTime(new Date(project.created_at)),
        timestamp: new Date(project.created_at)
      })
    })

    // Get recent tasks (with error handling)
    try {
      const { data: recentTasks, error: tasksError } = await supabase
        .from('tasks')
        .select('id, title, status, created_at, updated_at, project_id, projects(project_name)')
        .in('project_id',
          (await supabase
            .from('projects')
            .select('id')
            .eq('user_id', userId)
          ).data?.map(p => p.id) || []
        )
        .order('updated_at', { ascending: false })
        .limit(5)

      if (tasksError) {
        console.error('Error fetching recent tasks for activity:', tasksError)
      } else {
        recentTasks?.forEach(task => {
          activities.push({
            id: `task-${task.id}`,
            type: 'ticket',
            user: {
              name: 'You',
              initials: 'YU'
            },
            project: (task.projects as any)?.project_name || 'Unknown Project',
            action: task.status === 'done' ? 'completed task' : 'updated task',
            target: task.title,
            time: getRelativeTime(new Date(task.updated_at)),
            timestamp: new Date(task.updated_at)
          })
        })
      }
    } catch (error) {
      console.error('Error in tasks activity fetch:', error)
    }

    // Get recent validations
    const { data: recentValidations } = await supabase
      .from('idea_validations')
      .select('id, idea_title, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(3)

    recentValidations?.forEach(validation => {
      activities.push({
        id: `validation-${validation.id}`,
        type: 'validation',
        user: {
          name: 'You',
          initials: 'YU'
        },
        action: 'validated idea',
        target: validation.idea_title,
        time: getRelativeTime(new Date(validation.created_at)),
        timestamp: new Date(validation.created_at)
      })
    })

    // Sort by timestamp and return latest 8
    return activities
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 8)

  } catch (error) {
    console.error('Error fetching dashboard activity:', error)
    return []
  }
}

// Helper function to get relative time
function getRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`
  return date.toLocaleDateString()
}
