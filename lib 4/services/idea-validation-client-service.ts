"use client";

import { createClient } from '@/lib/supabase/client'
import { IdeaValidationRecord } from './idea-validation-db-service'

// Get current user ID
export async function getCurrentUserId(): Promise<string | null> {
  try {
    const supabase = createClient()
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error) {
      console.error('Error getting current user:', error)
      return null
    }
    
    return user?.id || null
  } catch (error) {
    console.error('Error in getCurrentUserId:', error)
    return null
  }
}

// Get user validations (client-side)
export async function getUserValidationsClient(): Promise<IdeaValidationRecord[]> {
  try {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return []
    }
    
    const { data, error } = await supabase
      .from('idea_validations')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Error getting user validations:', error)
      return []
    }
    
    return data || []
  } catch (error) {
    console.error('Error in getUserValidationsClient:', error)
    return []
  }
}

// Delete validation (client-side)
export async function deleteValidationClient(id: string): Promise<boolean> {
  try {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }
    
    const { error } = await supabase
      .from('idea_validations')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)
    
    if (error) {
      console.error('Error deleting validation:', error)
      throw new Error(`Failed to delete validation: ${error.message}`)
    }
    
    return true
  } catch (error) {
    console.error('Error in deleteValidationClient:', error)
    throw error
  }
}

// Check if user is authenticated
export async function isUserAuthenticated(): Promise<boolean> {
  try {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    return !!user
  } catch (error) {
    console.error('Error checking authentication:', error)
    return false
  }
}
