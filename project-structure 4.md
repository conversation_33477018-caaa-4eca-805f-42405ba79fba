# SaaS Ideas - Project Structure

```
saas-ideas/
├── .env.local                    # Environment variables
├── .gitignore                    # Git ignore file
├── package.json                  # Project dependencies
├── next.config.js                # Next.js configuration
├── tsconfig.json                 # TypeScript configuration
├── public/                       # Static assets
│   ├── favicon.ico
│   ├── logo.svg
│   └── images/
├── docs/                         # Project documentation
│   ├── design-system.md          # Overall design system documentation
│   ├── ui-components/            # UI component documentation
│   │   ├── buttons.md
│   │   ├── cards.md
│   │   ├── forms.md
│   │   ├── navigation.md
│   │   └── ...                   # Other component docs
│   ├── styles/                   # Styling guidelines and documentation
│   │   ├── colors.md
│   │   ├── typography.md
│   │   ├── spacing.md
│   │   ├── responsive.md
│   │   └── ...                   # Other style docs
│   ├── reactbits-components/     # External component library documentation
│   │   ├── index.md              # Main index of all reactbits components
│   │   ├── text-animations-index.md  # Index of text animation components
│   │   ├── text-animations.md    # SplitText and RotatingText components
│   │   ├── text-animations-2.md  # FuzzyText, CountUp, and TextCursor components
│   │   ├── text-animations-3.md  # ScrollFloat component
│   │   └── ...                   # Other component categories
│   └── templates/                # Documentation templates
│       └── component-template.md # Template for component documentation
├── src/
│   ├── app/                      # App router pages
│   │   ├── layout.tsx            # Root layout
│   │   ├── page.tsx              # Landing page
│   │   ├── auth/
│   │   │   ├── login/page.tsx    # Login page
│   │   │   └── register/page.tsx # Register page
│   │   ├── studio/              # All app features under studio
│   │   │   ├── layout.tsx        # Studio layout with sidebar
│   │   │   ├── page.tsx          # Studio dashboard home page
│   │   │   ├── ai-insights/      # AI Insights section
│   │   │   │   ├── page.tsx      # AI analysis page with project list
│   │   │   │   └── [projectId]/  # Project pages accessed from AI Insights
│   │   │   │       ├── page.tsx      # Project overview
│   │   │   │       ├── user-flow/page.tsx  # User flow editor
│   │   │   │       ├── tickets/page.tsx   # Kanban tickets board
│   │   │   │       └── memory-bank/page.tsx # Memory bank
│   │   │   ├── user-management/page.tsx # User management
│   │   │   └── settings/page.tsx  # Settings page
│   │   └── api/                  # API routes
│   │       ├── auth/
│   │       │   ├── register/route.ts
│   │       │   └── login/route.ts
│   │       ├── projects/
│   │       │   ├── route.ts
│   │       │   └── [id]/
│   │       │       ├── route.ts
│   │       │       ├── features/route.ts
│   │       │       └── userflow/route.ts
│   │       └── analysis/
│   │           └── route.ts
│   ├── components/               # Reusable components
│   │   ├── ui/                   # UI components (shadcn)
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── input.tsx
│   │   │   └── tabs.tsx
│   │   ├── animations/           # Animation components from reactbits
│   │   │   ├── text/             # Text animation components
│   │   │   │   ├── split-text.tsx
│   │   │   │   ├── rotating-text.tsx
│   │   │   │   ├── fuzzy-text.tsx
│   │   │   │   ├── count-up.tsx
│   │   │   │   ├── text-cursor.tsx
│   │   │   │   └── scroll-float.tsx
│   │   │   └── motion/           # Motion animation components
│   │   ├── layout/
│   │   │   ├── sidebar-nav.tsx   # Side navigation
│   │   │   ├── header.tsx
│   │   │   └── footer.tsx
│   │   ├── studio/              # Studio components
│   │   │   ├── project-card.tsx
│   │   │   ├── stats-card.tsx
│   │   │   ├── ai-insights/
│   │   │   │   ├── ai-analysis-form.tsx
│   │   │   │   ├── pillar-score.tsx
│   │   │   │   ├── analysis-card.tsx
│   │   │   │   └── radar-chart.tsx
│   │   │   ├── projects/
│   │   │   │   ├── userflow/
│   │   │   │   │   ├── flow-editor.tsx
│   │   │   │   │   └── flow-node.tsx
│   │   │   │   ├── tickets/
│   │   │   │   │   ├── kanban-board.tsx
│   │   │   │   │   └── ticket-card.tsx
│   │   │   │   └── memory-bank/
│   │   │   │       ├── snippet-explorer.tsx
│   │   │   │       └── ai-insights.tsx
│   ├── lib/                      # Utility functions and libraries
│   │   ├── supabase/             # Supabase client and server
│   │   │   ├── client.ts
│   │   │   └── server.ts
│   │   ├── services/             # Business logic services
│   │   │   ├── gemini-service.ts # Gemini AI integration
│   │   │   └── supabase-service.ts # Database operations
│   │   ├── ai/                   # AI integration (legacy)
│   │   │   ├── analysis.ts
│   │   │   └── cursor.ts
│   │   └── utils/                # Helper functions
│   │       ├── auth.ts
│   │       └── formatting.ts
│   ├── hooks/                    # Custom React hooks
│   │   ├── use-auth.ts
│   │   ├── use-projects.ts
│   │   └── use-analysis.ts
│   ├── types/                    # TypeScript types
│   │   ├── project.ts
│   │   ├── analysis.ts
│   │   └── user.ts
│   └── styles/                   # Global styles
│       └── globals.css
└── README.md                     # Project documentation
```

## Navigation Structure

### Side Navigation
- Dashboard
- AI Insights (stores analyzed projects)
- User Management
- Settings

### Project Navigation (Tabs within Projects)
- User Flow
- Tickets Board
- Overview
- Memory Bank

## Key Components

### SidebarNav Component
The sidebar navigation component provides access to all main sections:

```typescript
// src/components/layout/sidebar-nav.tsx
export default function SidebarNav() {
  return (
    <nav className="h-full w-64 bg-gray-900 text-white p-4">
      <div className="mb-8">
        <Logo />
      </div>
      
      <div className="space-y-1">
        <NavItem href="/studio" icon={HomeIcon} label="Dashboard" />
        <NavItem href="/studio/ai-insights" icon={BrainIcon} label="AI Insights" />
        <NavItem href="/studio/user-management" icon={UsersIcon} label="User Management" />
        <NavItem href="/studio/settings" icon={SettingsIcon} label="Settings" />
      </div>
    </nav>
  );
}
```

### Project Tabs Component
The tabs for navigating within a project:

```typescript
// src/components/layout/project-tabs.tsx
export default function ProjectTabs({ projectId }: { projectId: string }) {
  return (
    <Tabs defaultValue="user-flow">
      <TabsList>
        <TabsTrigger value="user-flow">User Flow</TabsTrigger>
        <TabsTrigger value="tickets">Tickets Board</TabsTrigger>
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="memory-bank">Memory Bank</TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
```

## Data Flow

1. User logs in and is directed to the Studio dashboard home page
2. User navigates to AI Insights to analyze new ideas or view previous analyses
3. When a project is selected from AI Insights, the user is taken to the project page with tabs for navigation
4. The project data is loaded from Supabase and displayed in the appropriate sections
5. **AI Task Generation**: When visiting tasks page, system checks for cached tasks first
6. **Smart Caching**: If no cached tasks exist, Gemini AI generates new tasks and explanations
7. **Persistent Storage**: Generated tasks and explanations are saved to Supabase for future visits
8. **Regeneration**: Users can force regenerate tasks which updates the cache with new AI content
9. Changes are saved in real-time to Supabase

## State Management

- Authentication state is managed using Supabase Auth
- Project data is fetched using React Query for efficient caching and updates
- UI state is managed using React Context API
- Form state is managed using React Hook Form 

## Documentation Structure

The project includes comprehensive documentation for UI components and styles:

### UI Component Documentation
- **88 pages** of detailed documentation covering all UI components
- Usage examples with code snippets
- Props and configuration options
- Accessibility guidelines
- Interactive examples

### Style Documentation
- Color system and theme variables
- Typography scales and font usage
- Spacing and layout guidelines
- Responsive design patterns
- Animation and transition standards

### Reactbits Component Documentation
- Organized by component category (text animations, motion, etc.)
- Each component includes:
  - Overview and best use cases
  - Installation requirements
  - Props documentation
  - Usage examples
  - Implementation code
  - Accessibility considerations
- Main index provides a quick reference to all components

### Integration with Development Workflow
- Components in the codebase are linked to their documentation
- Style tokens in the code match the documented design system
- Documentation is versioned alongside the codebase
- External components are properly attributed and documented

You can access the full documentation by opening the markdown files in the `/docs` directory. 