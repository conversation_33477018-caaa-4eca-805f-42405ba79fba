// Script to apply user management migration to Supabase
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_KEY');
  console.error('\nPlease check your .env.local file.');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  try {
    console.log('🚀 Starting user management migration...\n');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'user-management-migration.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Migration file loaded successfully');
    console.log(`📍 File: ${migrationPath}`);
    console.log(`📏 Size: ${migrationSQL.length} characters\n`);

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`🔧 Found ${statements.length} SQL statements to execute\n`);

    // Execute each statement
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip comments and empty statements
      if (!statement || statement.startsWith('--') || statement.trim() === '') {
        continue;
      }

      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
        
        // Execute the SQL statement
        const { data, error } = await supabase.rpc('exec_sql', { 
          sql: statement + ';' 
        });

        if (error) {
          // Try direct execution if RPC fails
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0);
          
          if (directError) {
            // Execute using raw SQL
            const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${supabaseServiceKey}`,
                'apikey': supabaseServiceKey
              },
              body: JSON.stringify({ sql: statement + ';' })
            });

            if (!response.ok) {
              throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
          }
        }

        successCount++;
        console.log(`✅ Statement ${i + 1} executed successfully`);
        
      } catch (error) {
        errorCount++;
        console.error(`❌ Error executing statement ${i + 1}:`);
        console.error(`   SQL: ${statement.substring(0, 100)}...`);
        console.error(`   Error: ${error.message}\n`);
        
        // Continue with other statements unless it's a critical error
        if (error.message.includes('already exists') || 
            error.message.includes('does not exist') ||
            error.message.includes('duplicate')) {
          console.log('   ℹ️  This error is likely safe to ignore (resource already exists)\n');
        } else {
          console.log('   ⚠️  This might be a critical error, but continuing...\n');
        }
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`   ✅ Successful statements: ${successCount}`);
    console.log(`   ❌ Failed statements: ${errorCount}`);
    console.log(`   📝 Total statements: ${statements.length}`);

    if (errorCount === 0) {
      console.log('\n🎉 User management migration completed successfully!');
      console.log('\n📋 What was added:');
      console.log('   • Extended user table with management fields');
      console.log('   • User activity logs table');
      console.log('   • User sessions table');
      console.log('   • Indexes for better performance');
      console.log('   • Row Level Security policies');
      console.log('   • Helper functions for user management');
      console.log('   • User statistics view');
      console.log('\n🔗 You can now access the user management page at:');
      console.log('   http://localhost:3000/studio/users');
    } else {
      console.log('\n⚠️  Migration completed with some errors.');
      console.log('   Please review the errors above and check your database.');
      console.log('   Some errors might be expected (e.g., "already exists" errors).');
    }

  } catch (error) {
    console.error('\n💥 Migration failed with error:');
    console.error(error.message);
    console.error('\n🔍 Troubleshooting tips:');
    console.error('   1. Check your Supabase credentials in .env.local');
    console.error('   2. Ensure your Supabase project is accessible');
    console.error('   3. Verify you have the correct service role key');
    console.error('   4. Check if the migration file exists and is readable');
    process.exit(1);
  }
}

// Test database connection first
async function testConnection() {
  try {
    console.log('🔍 Testing database connection...');
    
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }

    console.log('✅ Database connection successful\n');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error(`   ${error.message}\n`);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🏗️  User Management Migration Tool');
  console.log('=====================================\n');

  // Test connection first
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.error('❌ Cannot proceed without database connection');
    process.exit(1);
  }

  // Apply migration
  await applyMigration();
}

// Run the migration
main().catch(error => {
  console.error('\n💥 Unexpected error:', error);
  process.exit(1);
});
