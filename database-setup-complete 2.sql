-- Complete Database Setup for SaaS Ideas Application
-- Run this in your Supabase SQL Editor

-- Step 1: Create projects table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  project_name TEXT NOT NULL,
  project_description TEXT NOT NULL,
  market_feasibility JSONB NOT NULL,
  suggested_improvements JSONB NOT NULL,
  core_features JSONB NOT NULL,
  technical_requirements JSONB NOT NULL,
  pricing_model JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  progress INTEGER DEFAULT 0,
  -- AI-generated content storage
  generated_tasks JSONB,
  tasks_explanation TEXT,
  tasks_generated_at TIMESTAMPTZ,
  -- User flow data
  user_flow_data JSONB,
  user_flow_generated_at TIMESTAMPTZ,
  -- Memory bank data
  memory_bank_data JSONB,
  memory_bank_generated_at TIMESTAMPTZ,
  -- Overview data
  overview_data JSONB,
  overview_generated_at TIMESTAMPTZ
);

-- Step 2: Create tasks table for kanban board
CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'done')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  category TEXT,
  estimated_hours INTEGER,
  dependencies JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Step 3: Add foreign key constraint for tasks table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'tasks_project_id_fkey'
  ) THEN
    ALTER TABLE public.tasks 
    ADD CONSTRAINT tasks_project_id_fkey 
    FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Step 4: Create indexes for better performance
CREATE INDEX IF NOT EXISTS projects_user_id_idx ON public.projects (user_id);
CREATE INDEX IF NOT EXISTS projects_created_at_idx ON public.projects (created_at);
CREATE INDEX IF NOT EXISTS tasks_project_id_idx ON public.tasks (project_id);
CREATE INDEX IF NOT EXISTS tasks_status_idx ON public.tasks (status);
CREATE INDEX IF NOT EXISTS tasks_created_at_idx ON public.tasks (created_at);

-- Step 5: Enable Row Level Security
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Step 6: Drop existing policies if they exist
DROP POLICY IF EXISTS select_own_projects ON public.projects;
DROP POLICY IF EXISTS insert_own_projects ON public.projects;
DROP POLICY IF EXISTS update_own_projects ON public.projects;
DROP POLICY IF EXISTS delete_own_projects ON public.projects;

DROP POLICY IF EXISTS select_own_tasks ON public.tasks;
DROP POLICY IF EXISTS insert_own_tasks ON public.tasks;
DROP POLICY IF EXISTS update_own_tasks ON public.tasks;
DROP POLICY IF EXISTS delete_own_tasks ON public.tasks;

-- Step 7: Create RLS policies for projects table
CREATE POLICY select_own_projects 
  ON public.projects 
  FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY insert_own_projects 
  ON public.projects 
  FOR INSERT 
  WITH CHECK (user_id = auth.uid());

CREATE POLICY update_own_projects 
  ON public.projects 
  FOR UPDATE 
  USING (user_id = auth.uid());

CREATE POLICY delete_own_projects 
  ON public.projects 
  FOR DELETE 
  USING (user_id = auth.uid());

-- Step 8: Create RLS policies for tasks table
CREATE POLICY select_own_tasks 
  ON public.tasks 
  FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.projects 
    WHERE projects.id = tasks.project_id 
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY insert_own_tasks 
  ON public.tasks 
  FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.projects 
    WHERE projects.id = tasks.project_id 
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY update_own_tasks 
  ON public.tasks 
  FOR UPDATE 
  USING (EXISTS (
    SELECT 1 FROM public.projects 
    WHERE projects.id = tasks.project_id 
    AND projects.user_id = auth.uid()
  ));

CREATE POLICY delete_own_tasks 
  ON public.tasks 
  FOR DELETE 
  USING (EXISTS (
    SELECT 1 FROM public.projects 
    WHERE projects.id = tasks.project_id 
    AND projects.user_id = auth.uid()
  ));

-- Step 9: Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 10: Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_projects_updated_at ON public.projects;
CREATE TRIGGER update_projects_updated_at
BEFORE UPDATE ON public.projects
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

DROP TRIGGER IF EXISTS update_tasks_updated_at ON public.tasks;
CREATE TRIGGER update_tasks_updated_at
BEFORE UPDATE ON public.tasks
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Step 11: Verify setup
SELECT 
  'Projects table exists' as check_name,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects') 
    THEN 'PASS' ELSE 'FAIL' END as status
UNION ALL
SELECT 
  'Tasks table exists' as check_name,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tasks') 
    THEN 'PASS' ELSE 'FAIL' END as status
UNION ALL
SELECT 
  'Foreign key constraint exists' as check_name,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'tasks_project_id_fkey') 
    THEN 'PASS' ELSE 'FAIL' END as status;

-- Success message
SELECT 'Database setup completed successfully!' as message;
