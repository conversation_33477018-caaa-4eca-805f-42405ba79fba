"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three";
exports.ids = ["vendor-chunks/@react-three"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-three/drei/core/Fbo.js":
/*!****************************************************!*\
  !*** ./node_modules/@react-three/drei/core/Fbo.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fbo: () => (/* binding */ Fbo),\n/* harmony export */   useFBO: () => (/* binding */ useFBO)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n\n\n\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(/** Width in pixels, or settings (will render fullscreen by default) */\nwidth, /** Height in pixels */\nheight, /**Settings */\nsettings) {\n  const size = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.A)(state => state.size);\n  const viewport = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.A)(state => state.viewport);\n  const _width = typeof width === 'number' ? width : size.width * viewport.dpr;\n  const _height = typeof height === 'number' ? height : size.height * viewport.dpr;\n  const _settings = (typeof width === 'number' ? settings : width) || {};\n  const {\n    samples = 0,\n    depth,\n    ...targetSettings\n  } = _settings;\n  const target = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const target = new three__WEBPACK_IMPORTED_MODULE_2__.WebGLRenderTarget(_width, _height, {\n      minFilter: three__WEBPACK_IMPORTED_MODULE_2__.LinearFilter,\n      magFilter: three__WEBPACK_IMPORTED_MODULE_2__.LinearFilter,\n      type: three__WEBPACK_IMPORTED_MODULE_2__.HalfFloatType,\n      ...targetSettings\n    });\n    if (depth) {\n      target.depthTexture = new three__WEBPACK_IMPORTED_MODULE_2__.DepthTexture(_width, _height, three__WEBPACK_IMPORTED_MODULE_2__.FloatType);\n    }\n    target.samples = samples;\n    return target;\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    target.setSize(_width, _height);\n    if (samples) target.samples = samples;\n  }, [samples, target, _width, _height]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => target.dispose();\n  }, []);\n  return target;\n}\nconst Fbo = ({\n  children,\n  width,\n  height,\n  ...settings\n}) => {\n  const target = useFBO(width, height, settings);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children == null ? void 0 : children(target));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/Fbo.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/PerspectiveCamera.js":
/*!******************************************************************!*\
  !*** ./node_modules/@react-three/drei/core/PerspectiveCamera.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerspectiveCamera: () => (/* binding */ PerspectiveCamera)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var _Fbo_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Fbo.js */ \"(ssr)/./node_modules/@react-three/drei/core/Fbo.js\");\n\n\n\n\n\nconst isFunction = node => typeof node === 'function';\nconst PerspectiveCamera = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  makeDefault,\n  children,\n  ...props\n}, ref) => {\n  const set = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(({\n    set\n  }) => set);\n  const camera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(({\n    camera\n  }) => camera);\n  const size = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(({\n    size\n  }) => size);\n  const cameraRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const fbo = (0,_Fbo_js__WEBPACK_IMPORTED_MODULE_3__.useFBO)(resolution);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.aspect = size.width / size.height;\n    }\n  }, [size, props]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.C)(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"perspectiveCamera\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/PerspectiveCamera.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ useThree),\n/* harmony export */   B: () => (/* binding */ Block),\n/* harmony export */   C: () => (/* binding */ useFrame),\n/* harmony export */   D: () => (/* binding */ useGraph),\n/* harmony export */   E: () => (/* binding */ ErrorBoundary),\n/* harmony export */   F: () => (/* binding */ useLoader),\n/* harmony export */   _: () => (/* binding */ _roots),\n/* harmony export */   a: () => (/* binding */ useMutableCallback),\n/* harmony export */   b: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   c: () => (/* binding */ createRoot),\n/* harmony export */   d: () => (/* binding */ unmountComponentAtNode),\n/* harmony export */   e: () => (/* binding */ extend),\n/* harmony export */   f: () => (/* binding */ createPointerEvents),\n/* harmony export */   g: () => (/* binding */ createEvents),\n/* harmony export */   h: () => (/* binding */ flushGlobalEffects),\n/* harmony export */   i: () => (/* binding */ isRef),\n/* harmony export */   j: () => (/* binding */ addEffect),\n/* harmony export */   k: () => (/* binding */ addAfterEffect),\n/* harmony export */   l: () => (/* binding */ addTail),\n/* harmony export */   m: () => (/* binding */ invalidate),\n/* harmony export */   n: () => (/* binding */ advance),\n/* harmony export */   o: () => (/* binding */ createPortal),\n/* harmony export */   p: () => (/* binding */ context),\n/* harmony export */   q: () => (/* binding */ applyProps),\n/* harmony export */   r: () => (/* binding */ reconciler),\n/* harmony export */   s: () => (/* binding */ getRootState),\n/* harmony export */   t: () => (/* binding */ threeTypes),\n/* harmony export */   u: () => (/* binding */ useBridge),\n/* harmony export */   v: () => (/* binding */ dispose),\n/* harmony export */   w: () => (/* binding */ act),\n/* harmony export */   x: () => (/* binding */ buildGraph),\n/* harmony export */   y: () => (/* binding */ useInstanceHandle),\n/* harmony export */   z: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var zustand_traditional__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zustand/traditional */ \"(ssr)/./node_modules/zustand/esm/traditional.mjs\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/scheduler/index.js\");\n/* harmony import */ var suspend_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! suspend-react */ \"(ssr)/./node_modules/suspend-react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n\n\n\n\n\n\n\n\n\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\n/**\r\n * Returns the instance's initial (outmost) root.\r\n */\nfunction findInitialRoot(instance) {\n  let root = instance.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n/**\r\n * Safely flush async effects when testing, simulating a legacy root.\r\n * @deprecated Import from React instead. import { act } from 'react'\r\n */\n// Reference with computed key to break Webpack static analysis\n// https://github.com/webpack/webpack/issues/14814\nconst act = react__WEBPACK_IMPORTED_MODULE_0__['act' + ''];\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\nconst isColorRepresentation = value => value != null && (typeof value === 'string' || typeof value === 'number' || value.isColor);\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = /* @__PURE__ */((_window$document, _window$navigator) => typeof window !== 'undefined' && (((_window$document = window.document) == null ? void 0 : _window$document.createElement) || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative'))() ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\n/**\r\n * Bridges renderer Context and StrictMode from a primary renderer.\r\n */\nfunction useBridge() {\n  const fiber = (0,its_fine__WEBPACK_IMPORTED_MODULE_5__.useFiber)();\n  const ContextBridge = (0,its_fine__WEBPACK_IMPORTED_MODULE_5__.useContextBridge)();\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    children\n  }) => {\n    const strict = !!(0,its_fine__WEBPACK_IMPORTED_MODULE_5__.traverseFiber)(fiber, true, node => node.type === react__WEBPACK_IMPORTED_MODULE_0__.StrictMode);\n    const Root = strict ? react__WEBPACK_IMPORTED_MODULE_0__.StrictMode : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Root, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ContextBridge, {\n        children: children\n      })\n    });\n  }, [fiber, ContextBridge]);\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\n\n// NOTE: static members get down-level transpiled to mutations which break tree-shaking\nconst ErrorBoundary = /* @__PURE__ */(_ErrorBoundary => (_ErrorBoundary = class ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}, _ErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n}), _ErrorBoundary))();\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nfunction getRootState(obj) {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  nul: a => a === null,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {},\n    meshes: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n      if (obj.isMesh && !data.meshes[obj.name]) data.meshes[obj.name] = obj;\n    });\n  }\n  return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.type !== 'Scene') obj.dispose == null ? void 0 : obj.dispose();\n  for (const p in obj) {\n    const prop = obj[p];\n    if ((prop == null ? void 0 : prop.type) !== 'Scene') prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n  }\n}\nconst REACT_INTERNAL_PROPS = ['children', 'key', 'ref'];\n\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n  const props = {};\n  for (const key in queue) {\n    if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n  }\n  return props;\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n  const object = target;\n\n  // Create instance descriptor\n  let instance = object == null ? void 0 : object.__r3f;\n  if (!instance) {\n    instance = {\n      root,\n      type,\n      parent: null,\n      children: [],\n      props: getInstanceProps(props),\n      object,\n      eventCount: 0,\n      handlers: {},\n      isHidden: false\n    };\n    if (object) object.__r3f = instance;\n  }\n  return instance;\n}\nfunction resolve(root, key) {\n  let target = root[key];\n  if (!key.includes('-')) return {\n    root,\n    key,\n    target\n  };\n\n  // Resolve pierced target\n  target = root;\n  for (const part of key.split('-')) {\n    var _target;\n    key = part;\n    root = target;\n    target = (_target = target) == null ? void 0 : _target[key];\n  }\n\n  // TODO: change key to 'foo-bar' if target is undefined?\n\n  return {\n    root,\n    key,\n    target\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n  if (is.str(child.props.attach)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(child.props.attach)) {\n      const index = child.props.attach.replace(INDEX_REGEX, '');\n      const {\n        root,\n        key\n      } = resolve(parent.object, index);\n      if (!Array.isArray(root[key])) root[key] = [];\n    }\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    child.previousAttach = root[key];\n    root[key] = child.object;\n  } else if (is.fun(child.props.attach)) {\n    child.previousAttach = child.props.attach(parent.object, child.object);\n  }\n}\nfunction detach(parent, child) {\n  if (is.str(child.props.attach)) {\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    const previous = child.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete root[key];\n    // Otherwise set the previous value\n    else root[key] = previous;\n  } else {\n    child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n  }\n  delete child.previousAttach;\n}\nconst RESERVED_PROPS = [...REACT_INTERNAL_PROPS,\n// Instance props\n'args', 'dispose', 'attach', 'object', 'onUpdate',\n// Behavior flags\n'dispose'];\nconst MEMOIZED_PROTOTYPES = new Map();\nfunction getMemoizedPrototype(root) {\n  let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n  try {\n    if (!ctor) {\n      ctor = new root.constructor();\n      MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n    }\n  } catch (e) {\n    // ...\n  }\n  return ctor;\n}\n\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n  const changedProps = {};\n\n  // Sort through props\n  for (const prop in newProps) {\n    // Skip reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n    // Skip if props match\n    if (is.equ(newProps[prop], instance.props[prop])) continue;\n\n    // Props changed, add them\n    changedProps[prop] = newProps[prop];\n\n    // Reset pierced props\n    for (const other in newProps) {\n      if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n    }\n  }\n\n  // Reset removed props for HMR\n  for (const prop in instance.props) {\n    if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n    const {\n      root,\n      key\n    } = resolve(instance.object, prop);\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (root.constructor && root.constructor.length === 0) {\n      // create a blank slate of the instance and copy the particular parameter.\n      const ctor = getMemoizedPrototype(root);\n      if (!is.und(ctor)) changedProps[key] = ctor[key];\n    } else {\n      // instance does not have constructor, just set it to 0\n      changedProps[key] = 0;\n    }\n  }\n  return changedProps;\n}\n\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = ['map', 'emissiveMap', 'sheenColorMap', 'specularColorMap', 'envMap'];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n  var _instance$object;\n  const instance = object.__r3f;\n  const rootState = instance && findInitialRoot(instance).getState();\n  const prevHandlers = instance == null ? void 0 : instance.eventCount;\n  for (const prop in props) {\n    let value = props[prop];\n\n    // Don't mutate reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n\n    // Deal with pointer events, including removing them if undefined\n    if (instance && EVENT_REGEX.test(prop)) {\n      if (typeof value === 'function') instance.handlers[prop] = value;else delete instance.handlers[prop];\n      instance.eventCount = Object.keys(instance.handlers).length;\n      continue;\n    }\n\n    // Ignore setting undefined props\n    // https://github.com/pmndrs/react-three-fiber/issues/274\n    if (value === undefined) continue;\n    let {\n      root,\n      key,\n      target\n    } = resolve(object, prop);\n\n    // Layers must be written to the mask property\n    if (target instanceof three__WEBPACK_IMPORTED_MODULE_6__.Layers && value instanceof three__WEBPACK_IMPORTED_MODULE_6__.Layers) {\n      target.mask = value.mask;\n    }\n    // Set colors if valid color representation for automatic conversion (copy)\n    else if (target instanceof three__WEBPACK_IMPORTED_MODULE_6__.Color && isColorRepresentation(value)) {\n      target.set(value);\n    }\n    // Copy if properties match signatures and implement math interface (likely read-only)\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof target.copy === 'function' && value != null && value.constructor && target.constructor === value.constructor) {\n      target.copy(value);\n    }\n    // Set array types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && Array.isArray(value)) {\n      if (typeof target.fromArray === 'function') target.fromArray(value);else target.set(...value);\n    }\n    // Set literal types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof value === 'number') {\n      // Allow setting array scalars\n      if (typeof target.setScalar === 'function') target.setScalar(value);\n      // Otherwise just set single value\n      else target.set(value);\n    }\n    // Else, just overwrite the value\n    else {\n      var _root$key;\n      root[key] = value;\n\n      // Auto-convert sRGB texture parameters for built-in materials\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      // https://github.com/mrdoob/three.js/pull/25857\n      if (rootState && !rootState.linear && colorMaps.includes(key) && (_root$key = root[key]) != null && _root$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      root[key].format === three__WEBPACK_IMPORTED_MODULE_6__.RGBAFormat && root[key].type === three__WEBPACK_IMPORTED_MODULE_6__.UnsignedByteType) {\n        // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n        root[key].colorSpace = three__WEBPACK_IMPORTED_MODULE_6__.SRGBColorSpace;\n      }\n    }\n  }\n\n  // Register event handlers\n  if (instance != null && instance.parent && rootState != null && rootState.internal && (_instance$object = instance.object) != null && _instance$object.isObject3D && prevHandlers !== instance.eventCount) {\n    const object = instance.object;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = rootState.internal.interaction.indexOf(object);\n    if (index > -1) rootState.internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (instance.eventCount && object.raycast !== null) {\n      rootState.internal.interaction.push(object);\n    }\n  }\n\n  // Auto-attach geometries and materials\n  if (instance && instance.props.attach === undefined) {\n    if (instance.object.isBufferGeometry) instance.props.attach = 'geometry';else if (instance.object.isMaterial) instance.props.attach = 'material';\n  }\n\n  // Instance was updated, request a frame\n  if (instance) invalidateInstance(instance);\n  return object;\n}\nfunction invalidateInstance(instance) {\n  var _instance$root;\n  if (!instance.parent) return;\n  instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n  const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n  // Do not mess with the camera if it belongs to the user\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  if (camera.manual) return;\n  if (isOrthographicCamera(camera)) {\n    camera.left = size.width / -2;\n    camera.right = size.width / 2;\n    camera.top = size.height / 2;\n    camera.bottom = size.height / -2;\n  } else {\n    camera.aspect = size.width / size.height;\n  }\n  camera.updateProjectionMatrix();\n}\nconst isObject3D = object => object == null ? void 0 : object.isObject3D;\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        let state = getRootState(hit.object);\n\n        // If the object is not managed by R3F, it might be parented to an element which is.\n        // Traverse upwards until we find a managed parent and use its state instead.\n        if (!state) {\n          hit.object.traverseAncestors(obj => {\n            const parentState = getRootState(obj);\n            if (parentState) {\n              state = parentState;\n              return false;\n            }\n          });\n        }\n        if (state) {\n          const {\n            raycaster,\n            pointer,\n            camera,\n            internal\n          } = state;\n          const unprojectedPoint = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n          const hasPointerCapture = id => {\n            var _internal$capturedMap, _internal$capturedMap2;\n            return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n          };\n          const setPointerCapture = id => {\n            const captureData = {\n              intersection: hit,\n              target: event.target\n            };\n            if (internal.capturedMap.has(id)) {\n              // if the pointerId was previously captured, we add the hit to the\n              // event capturedMap.\n              internal.capturedMap.get(id).set(hit.eventObject, captureData);\n            } else {\n              // if the pointerId was not previously captured, we create a map\n              // containing the hitObject, and the hit. hitObject is used for\n              // faster access.\n              internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n            }\n            event.target.setPointerCapture(id);\n          };\n          const releasePointerCapture = id => {\n            const captures = internal.capturedMap.get(id);\n            if (captures) {\n              releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n            }\n          };\n\n          // Add native event props\n          let extractEventProps = {};\n          // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n          for (let prop in event) {\n            let property = event[prop];\n            // Only copy over atomics, leave functions alone as these should be\n            // called as event.nativeEvent.fn()\n            if (typeof property !== 'function') extractEventProps[prop] = property;\n          }\n          let raycastEvent = {\n            ...hit,\n            ...extractEventProps,\n            pointer,\n            intersections,\n            stopped: localState.stopped,\n            delta,\n            unprojectedPoint,\n            ray: raycaster.ray,\n            camera: camera,\n            // Hijack stopPropagation, which just sets a flag\n            stopPropagation() {\n              // https://github.com/pmndrs/react-three-fiber/issues/596\n              // Events are not allowed to stop propagation if the pointer has been captured\n              const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n              // We only authorize stopPropagation...\n              if (\n              // ...if this pointer hasn't been captured\n              !capturesForPointer ||\n              // ... or if the hit object is capturing the pointer\n              capturesForPointer.has(hit.eventObject)) {\n                raycastEvent.stopped = localState.stopped = true;\n                // Propagation is stopped, remove all other hover records\n                // An event handler is only allowed to flush other handlers if it is hovered itself\n                if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                  // Objects cannot flush out higher up objects that have already caught the event\n                  const higher = intersections.slice(0, intersections.indexOf(hit));\n                  cancelPointer([...higher, hit]);\n                }\n              }\n            },\n            // there should be a distinction between target and currentTarget\n            target: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            currentTarget: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            nativeEvent: event\n          };\n\n          // Call subscribers\n          callback(raycastEvent);\n          // Event bubbling may be interrupted by stopPropagation\n          if (localState.stopped === true) break;\n        }\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          const handlers = instance.handlers;\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n        const handlers = instance.handlers;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootStore = (0,zustand_traditional__WEBPACK_IMPORTED_MODULE_7__.createWithEqualityFn)((set, get) => {\n    const position = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n    const defaultTarget = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n    const tempTarget = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new three__WEBPACK_IMPORTED_MODULE_6__.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      scene: null,\n      xr: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new three__WEBPACK_IMPORTED_MODULE_6__.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, top = 0, left = 0) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top,\n          left\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        // Events\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        lastEvent: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createRef(),\n        // Updates\n        active: false,\n        frames: 0,\n        priority: 0,\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootStore.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootStore.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootStore.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      if (viewport.dpr > 0) gl.setPixelRatio(viewport.dpr);\n      const updateStyle = typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootStore.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootStore;\n};\n\n/**\r\n * Exposes an object's {@link Instance}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(instance, () => ref.current.__r3f, [ref]);\n  return instance;\n}\n\n/**\r\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\r\n */\nfunction useStore() {\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor$1 = value => {\n  var _value$prototype;\n  return typeof value === 'function' && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    let loader;\n\n    // Construct and cache loader if constructor was passed\n    if (isConstructor$1(Proto)) {\n      loader = memoizedLoaders.get(Proto);\n      if (!loader) {\n        loader = new Proto();\n        memoizedLoaders.set(Proto, loader);\n      }\n    } else {\n      loader = Proto;\n    }\n\n    // Apply loader extensions\n    if (extensions) extensions(loader);\n\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (isObject3D(data == null ? void 0 : data.scene)) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(loader, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.suspend)(loadingFn(extensions, onProgress), [loader, ...keys], {\n    equal: is.equ\n  });\n  // Return the object(s)\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (loader, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.preload)(loadingFn(extensions), [loader, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (loader, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.clear)([loader, ...keys]);\n};\n\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\n\nfunction createReconciler(config) {\n  const reconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_2___default()(config);\n  reconciler.injectIntoDevTools({\n    bundleType: typeof process !== 'undefined' && \"development\" !== 'production' ? 1 : 0,\n    rendererPackageName: '@react-three/fiber',\n    version: react__WEBPACK_IMPORTED_MODULE_0__.version\n  });\n  return reconciler;\n}\nconst NoEventPriority = 0;\n\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\n\nconst catalogue = {};\nconst PREFIX_REGEX = /^three(?=[A-Z])/;\nconst toPascalCase = type => `${type[0].toUpperCase()}${type.slice(1)}`;\nlet i = 0;\nconst isConstructor = object => typeof object === 'function';\nfunction extend(objects) {\n  if (isConstructor(objects)) {\n    const Component = `${i++}`;\n    catalogue[Component] = objects;\n    return Component;\n  } else {\n    Object.assign(catalogue, objects);\n  }\n}\nfunction validateInstance(type, props) {\n  // Get target from catalogue\n  const name = toPascalCase(type);\n  const target = catalogue[name];\n\n  // Validate element target\n  if (type !== 'primitive' && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n\n  // Validate primitives\n  if (type === 'primitive' && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n\n  // Throw if an object or literal was passed for args\n  if (props.args !== undefined && !Array.isArray(props.args)) throw new Error('R3F: The args prop must be an array!');\n}\nfunction createInstance(type, props, root) {\n  var _props$object;\n  // Remove three* prefix from elements if native element not present\n  type = toPascalCase(type) in catalogue ? type : type.replace(PREFIX_REGEX, '');\n  validateInstance(type, props);\n\n  // Regenerate the R3F instance for primitives to simulate a new object\n  if (type === 'primitive' && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n  return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n  if (!instance.isHidden) {\n    var _instance$parent;\n    if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n      detach(instance.parent, instance);\n    } else if (isObject3D(instance.object)) {\n      instance.object.visible = false;\n    }\n    instance.isHidden = true;\n    invalidateInstance(instance);\n  }\n}\nfunction unhideInstance(instance) {\n  if (instance.isHidden) {\n    var _instance$parent2;\n    if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n      attach(instance.parent, instance);\n    } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n      instance.object.visible = true;\n    }\n    instance.isHidden = false;\n    invalidateInstance(instance);\n  }\n}\n\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n  // Bail if tree isn't mounted or parent is not a container.\n  // This ensures that the tree is finalized and React won't discard results to Suspense\n  const state = child.root.getState();\n  if (!parent.parent && parent.object !== state.scene) return;\n\n  // Create & link object on first run\n  if (!child.object) {\n    var _child$props$object, _child$props$args;\n    // Get target from catalogue\n    const target = catalogue[toPascalCase(child.type)];\n\n    // Create object\n    child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...((_child$props$args = child.props.args) != null ? _child$props$args : []));\n    child.object.__r3f = child;\n  }\n\n  // Set initial props\n  applyProps(child.object, child.props);\n\n  // Append instance\n  if (child.props.attach) {\n    attach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n    if (beforeChild && childIndex !== -1) {\n      // If the child is already in the parent's children array, move it to the new position\n      // Otherwise, just insert it at the target position\n      const existingIndex = parent.object.children.indexOf(child.object);\n      if (existingIndex !== -1) {\n        parent.object.children.splice(existingIndex, 1);\n        const adjustedIndex = existingIndex < childIndex ? childIndex - 1 : childIndex;\n        parent.object.children.splice(adjustedIndex, 0, child.object);\n      } else {\n        child.object.parent = parent.object;\n        parent.object.children.splice(childIndex, 0, child.object);\n        child.object.dispatchEvent({\n          type: 'added'\n        });\n        parent.object.dispatchEvent({\n          type: 'childadded',\n          child: child.object\n        });\n      }\n    } else {\n      parent.object.add(child.object);\n    }\n  }\n\n  // Link subtree\n  for (const childInstance of child.children) handleContainerEffects(child, childInstance);\n\n  // Tree was updated, request a frame\n  invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n  if (!child) return;\n\n  // Link instances\n  child.parent = parent;\n  parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n  if (!child || !beforeChild) return;\n\n  // Link instances\n  child.parent = parent;\n  const childIndex = parent.children.indexOf(beforeChild);\n  if (childIndex !== -1) parent.children.splice(childIndex, 0, child);else parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n  if (typeof object.dispose === 'function') {\n    const handleDispose = () => {\n      try {\n        object.dispose();\n      } catch {\n        // no-op\n      }\n    };\n\n    // In a testing environment, cleanup immediately\n    if (typeof IS_REACT_ACT_ENVIRONMENT !== 'undefined') handleDispose();\n    // Otherwise, using a real GPU so schedule cleanup to prevent stalls\n    else (0,scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_scheduleCallback)(scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_IdlePriority, handleDispose);\n  }\n}\nfunction removeChild(parent, child, dispose) {\n  if (!child) return;\n\n  // Unlink instances\n  child.parent = null;\n  const childIndex = parent.children.indexOf(child);\n  if (childIndex !== -1) parent.children.splice(childIndex, 1);\n\n  // Eagerly tear down tree\n  if (child.props.attach) {\n    detach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    parent.object.remove(child.object);\n    removeInteractivity(findInitialRoot(child), child.object);\n  }\n\n  // Allow objects to bail out of unmount disposal with dispose={null}\n  const shouldDispose = child.props.dispose !== null && dispose !== false;\n\n  // Recursively remove instance children\n  for (let i = child.children.length - 1; i >= 0; i--) {\n    const node = child.children[i];\n    removeChild(child, node, shouldDispose);\n  }\n  child.children.length = 0;\n\n  // Unlink instance object\n  delete child.object.__r3f;\n\n  // Dispose object whenever the reconciler feels like it.\n  // Never dispose of primitives because their state may be kept outside of React!\n  // In order for an object to be able to dispose it\n  //   - has a dispose method\n  //   - cannot be a <primitive object={...} />\n  //   - cannot be a THREE.Scene, because three has broken its own API\n  if (shouldDispose && child.type !== 'primitive' && child.object.type !== 'Scene') {\n    disposeOnIdle(child.object);\n  }\n\n  // Tree was updated, request a frame for top-level instance\n  if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n  for (const _fiber of [fiber, fiber.alternate]) {\n    if (_fiber !== null) {\n      if (typeof _fiber.ref === 'function') {\n        _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n        const cleanup = _fiber.ref(publicInstance);\n        if (typeof cleanup === 'function') _fiber.refCleanup = cleanup;\n      } else if (_fiber.ref) {\n        _fiber.ref.current = publicInstance;\n      }\n    }\n  }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n  // Detach instance\n  for (const [instance] of reconstructed) {\n    const parent = instance.parent;\n    if (parent) {\n      if (instance.props.attach) {\n        detach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.remove(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          detach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.remove(child.object);\n        }\n      }\n    }\n\n    // If the old instance is hidden, we need to unhide it.\n    // React assumes it can discard instances since they're pure for DOM.\n    // This isn't true for us since our lifetimes are impure and longliving.\n    // So, we manually check if an instance was hidden and unhide it.\n    if (instance.isHidden) unhideInstance(instance);\n\n    // Dispose of old object if able\n    if (instance.object.__r3f) delete instance.object.__r3f;\n    if (instance.type !== 'primitive') disposeOnIdle(instance.object);\n  }\n\n  // Update instance\n  for (const [instance, props, fiber] of reconstructed) {\n    instance.props = props;\n    const parent = instance.parent;\n    if (parent) {\n      var _instance$props$objec, _instance$props$args;\n      // Get target from catalogue\n      const target = catalogue[toPascalCase(instance.type)];\n\n      // Create object\n      instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...((_instance$props$args = instance.props.args) != null ? _instance$props$args : []));\n      instance.object.__r3f = instance;\n      setFiberRef(fiber, instance.object);\n\n      // Set initial props\n      applyProps(instance.object, instance.props);\n      if (instance.props.attach) {\n        attach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.add(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          attach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.add(child.object);\n        }\n      }\n\n      // Tree was updated, request a frame\n      invalidateInstance(instance);\n    }\n  }\n  reconstructed.length = 0;\n}\n\n// Don't handle text instances, make it no-op\nconst handleTextInstance = () => {};\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = /* @__PURE__ */createReconciler({\n  isPrimaryRenderer: false,\n  warnsIfNotActing: false,\n  supportsMutation: true,\n  supportsPersistence: false,\n  supportsHydration: false,\n  createInstance,\n  removeChild,\n  appendChild,\n  appendInitialChild: appendChild,\n  insertBefore,\n  appendChildToContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    appendChild(scene, child);\n  },\n  removeChildFromContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    removeChild(scene, child);\n  },\n  insertInContainerBefore(container, child, beforeChild) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !beforeChild || !scene) return;\n    insertBefore(scene, child, beforeChild);\n  },\n  getRootHostContext: () => NO_CONTEXT,\n  getChildHostContext: () => NO_CONTEXT,\n  commitUpdate(instance, type, oldProps, newProps, fiber) {\n    var _newProps$args, _oldProps$args, _newProps$args2;\n    validateInstance(type, newProps);\n    let reconstruct = false;\n\n    // Reconstruct primitives if object prop changes\n    if (instance.type === 'primitive' && oldProps.object !== newProps.object) reconstruct = true;\n    // Reconstruct instance if args were added or removed\n    else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n    // Reconstruct instance if args were changed\n    else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index) => {\n      var _oldProps$args2;\n      return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n    })) reconstruct = true;\n\n    // Reconstruct when args or <primitive object={...} have changes\n    if (reconstruct) {\n      reconstructed.push([instance, {\n        ...newProps\n      }, fiber]);\n    } else {\n      // Create a diff-set, flag if there are any changes\n      const changedProps = diffProps(instance, newProps);\n      if (Object.keys(changedProps).length) {\n        Object.assign(instance.props, changedProps);\n        applyProps(instance.object, changedProps);\n      }\n    }\n\n    // Flush reconstructed siblings when we hit the last updated child in a sequence\n    const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n    if (isTailSibling) swapInstances();\n  },\n  finalizeInitialChildren: () => false,\n  commitMount() {},\n  getPublicInstance: instance => instance == null ? void 0 : instance.object,\n  prepareForCommit: () => null,\n  preparePortalMount: container => prepare(container.getState().scene, container, '', {}),\n  resetAfterCommit: () => {},\n  shouldSetTextContent: () => false,\n  clearContainer: () => false,\n  hideInstance,\n  unhideInstance,\n  createTextInstance: handleTextInstance,\n  hideTextInstance: handleTextInstance,\n  unhideTextInstance: handleTextInstance,\n  scheduleTimeout: typeof setTimeout === 'function' ? setTimeout : undefined,\n  cancelTimeout: typeof clearTimeout === 'function' ? clearTimeout : undefined,\n  noTimeout: -1,\n  getInstanceFromNode: () => null,\n  beforeActiveInstanceBlur() {},\n  afterActiveInstanceBlur() {},\n  detachDeletedInstance() {},\n  prepareScopeUpdate() {},\n  getInstanceFromScope: () => null,\n  shouldAttemptEagerTransition: () => false,\n  trackSchedulerEvent: () => {},\n  resolveEventType: () => null,\n  resolveEventTimeStamp: () => -1.1,\n  requestPostPaintCallback() {},\n  maySuspendCommit: () => false,\n  preloadInstance: () => true,\n  // true indicates already loaded\n  startSuspendingCommit() {},\n  suspendInstance() {},\n  waitForCommitToBeReady: () => null,\n  NotPendingTransition: null,\n  HostTransitionContext: /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_0__.createContext(null),\n  setCurrentUpdatePriority(newPriority) {\n    currentUpdatePriority = newPriority;\n  },\n  getCurrentUpdatePriority() {\n    return currentUpdatePriority;\n  },\n  resolveUpdatePriority() {\n    var _window$event;\n    if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n    switch (typeof window !== 'undefined' && ((_window$event = window.event) == null ? void 0 : _window$event.type)) {\n      case 'click':\n      case 'contextmenu':\n      case 'dblclick':\n      case 'pointercancel':\n      case 'pointerdown':\n      case 'pointerup':\n        return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DiscreteEventPriority;\n      case 'pointermove':\n      case 'pointerout':\n      case 'pointerover':\n      case 'pointerenter':\n      case 'pointerleave':\n      case 'wheel':\n        return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ContinuousEventPriority;\n      default:\n        return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n    }\n  },\n  resetFormInstance() {}\n});\n\nconst _roots = new Map();\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nfunction computeInitialSize(canvas, size) {\n  if (!size && typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left\n    };\n  } else if (!size && typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    ...size\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = _roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store,\n  // container\n  react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ConcurrentRoot,\n  // tag\n  null,\n  // hydration callbacks\n  false,\n  // isStrictMode\n  null,\n  // concurrentUpdatesByDefaultOverride\n  '',\n  // identifierPrefix\n  logRecoverableError,\n  // onUncaughtError\n  logRecoverableError,\n  // onCaughtError\n  logRecoverableError,\n  // onRecoverableError\n  null // transitionCallbacks\n  );\n  // Map it\n  if (!prevRoot) _roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let lastCamera;\n  let configured = false;\n  let pending = null;\n  return {\n    async configure(props = {}) {\n      let resolve;\n      pending = new Promise(_resolve => resolve = _resolve);\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) {\n        const defaultProps = {\n          canvas: canvas,\n          powerPreference: 'high-performance',\n          antialias: true,\n          alpha: true\n        };\n        const customRenderer = typeof glConfig === 'function' ? await glConfig(defaultProps) : glConfig;\n        if (isRenderer(customRenderer)) {\n          gl = customRenderer;\n        } else {\n          gl = new three__WEBPACK_IMPORTED_MODULE_9__.WebGLRenderer({\n            ...defaultProps,\n            ...glConfig\n          });\n        }\n        state.set({\n          gl\n        });\n      }\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new three__WEBPACK_IMPORTED_MODULE_6__.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions == null ? void 0 : cameraOptions.isCamera;\n        const camera = isCamera ? cameraOptions : orthographic ? new three__WEBPACK_IMPORTED_MODULE_6__.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new three__WEBPACK_IMPORTED_MODULE_6__.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if (!camera.manual) {\n              if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n                camera.manual = true;\n                camera.updateProjectionMatrix();\n              }\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n          prepare(scene, store, '', {});\n        } else {\n          scene = new three__WEBPACK_IMPORTED_MODULE_6__.Scene();\n          prepare(scene, store, '', {});\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene\n        });\n      }\n\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: three__WEBPACK_IMPORTED_MODULE_6__.BasicShadowMap,\n            percentage: three__WEBPACK_IMPORTED_MODULE_6__.PCFShadowMap,\n            soft: three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap,\n            variance: three__WEBPACK_IMPORTED_MODULE_6__.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n      three__WEBPACK_IMPORTED_MODULE_6__.ColorManagement.enabled = !legacy;\n\n      // Set color space and tonemapping preferences\n      if (!configured) {\n        gl.outputColorSpace = linear ? three__WEBPACK_IMPORTED_MODULE_6__.LinearSRGBColorSpace : three__WEBPACK_IMPORTED_MODULE_6__.SRGBColorSpace;\n        gl.toneMapping = flat ? three__WEBPACK_IMPORTED_MODULE_6__.NoToneMapping : three__WEBPACK_IMPORTED_MODULE_6__.ACESFilmicToneMapping;\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      resolve();\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured && !pending) this.configure();\n      pending.then(() => {\n        reconciler.updateContainer( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Provider, {\n          store: store,\n          children: children,\n          onCreated: onCreated,\n          rootElement: canvas\n        }), fiber, null, () => undefined);\n      });\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = _roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state.scene);\n            _roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Portal, {\n    children: children,\n    container: container,\n    state: state\n  });\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_6__.Raycaster());\n  const [pointer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_6__.Vector2());\n  const inject = useMutableCallback((rootState, injectState) => {\n    let viewport = undefined;\n    if (injectState.camera && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...rootState,\n      ...injectState,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...injectState.events,\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      // Layers are allowed to override events\n      setEvents: events => injectState.set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    };\n  });\n  const usePortalStore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const store = (0,zustand_traditional__WEBPACK_IMPORTED_MODULE_7__.createWithEqualityFn)((set, get) => ({\n      ...rest,\n      set,\n      get\n    }));\n\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const onMutate = prev => store.setState(state => inject.current(prev, state));\n    onMutate(previousRoot.getState());\n    previousRoot.subscribe(onMutate);\n    return store;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [previousRoot, container]);\n  return (\n    /*#__PURE__*/\n    // @ts-ignore, reconciler types are not maintained\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n      children: reconciler.createPortal( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(context.Provider, {\n        value: usePortalStore,\n        children: children\n      }), usePortalStore, null)\n    })\n  );\n}\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (let i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n  frame = requestAnimationFrame(loop);\n  running = true;\n  repeat = 0;\n\n  // Run effects\n  flushGlobalEffects('before', timestamp);\n\n  // Render all roots\n  useFrameInProgress = true;\n  for (const root of _roots.values()) {\n    var _state$gl$xr;\n    state = root.store.getState();\n\n    // If the frameloop is invalidated, do not run another frame\n    if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n      repeat += update(timestamp, state);\n    }\n  }\n  useFrameInProgress = false;\n\n  // Run after-effects\n  flushGlobalEffects('after', timestamp);\n\n  // Stop the loop if nothing invalidates it\n  if (repeat === 0) {\n    // Tail call effects, they are called when rendering stops\n    flushGlobalEffects('tail', timestamp);\n\n    // Flag end of operation\n    running = false;\n    return cancelAnimationFrame(frame);\n  }\n}\n\n/**\r\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\r\n */\nfunction invalidate(state, frames = 1) {\n  var _state$gl$xr2;\n  if (!state) return _roots.forEach(root => invalidate(root.store.getState(), frames));\n  if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n  if (frames > 1) {\n    // legacy support for people using frames parameters\n    // Increase frames, do not go higher than 60\n    state.internal.frames = Math.min(60, state.internal.frames + frames);\n  } else {\n    if (useFrameInProgress) {\n      //called from within a useFrame, it means the user wants an additional frame\n      state.internal.frames = 2;\n    } else {\n      //the user need a new frame, no need to increment further than 1\n      state.internal.frames = 1;\n    }\n  }\n\n  // If the render-loop isn't active, start it\n  if (!running) {\n    running = true;\n    requestAnimationFrame(loop);\n  }\n}\n\n/**\r\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\r\n */\nfunction advance(timestamp, runGlobalEffects = true, state, frame) {\n  if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n  if (!state) for (const root of _roots.values()) update(timestamp, root.store.getState());else update(timestamp, state, frame);\n  if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n}\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      if (events.handlers) {\n        for (const name in events.handlers) {\n          const event = events.handlers[name];\n          const [eventName, passive] = DOM_EVENTS[name];\n          target.addEventListener(eventName, event, {\n            passive\n          });\n        }\n      }\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        if (events.handlers) {\n          for (const name in events.handlers) {\n            const event = events.handlers[name];\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        }\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas),\n/* harmony export */   ReactThreeFiber: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   _roots: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__._),\n/* harmony export */   act: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   addAfterEffect: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   addEffect: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   addTail: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   advance: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   applyProps: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   buildGraph: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   context: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   createEvents: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   createPortal: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   createRoot: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   dispose: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   events: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   extend: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   flushGlobalEffects: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   getRootState: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   invalidate: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   reconciler: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   unmountComponentAtNode: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   useFrame: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   useGraph: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.D),\n/* harmony export */   useInstanceHandle: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   useLoader: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.F),\n/* harmony export */   useStore: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   useThree: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.A)\n/* harmony export */ });\n/* harmony import */ var _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./events-dc44c1b8.esm.js */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react_use_measure__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-use-measure */ \"(ssr)/./node_modules/react-use-measure/dist/index.js\");\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CanvasImpl({\n  ref,\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.f,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(three__WEBPACK_IMPORTED_MODULE_6__), []);\n  const Bridge = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)();\n  const [containerRef, containerRect] = (0,react_use_measure__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const divRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, () => canvasRef.current);\n  const handlePointerMissed = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(onPointerMissed);\n  const [block, setBlock] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n  const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.b)(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(canvas);\n      async function run() {\n        await root.current.configure({\n          gl,\n          scene,\n          events,\n          shadows,\n          linear,\n          flat,\n          legacy,\n          orthographic,\n          frameloop,\n          dpr,\n          performance,\n          raycaster,\n          camera,\n          size: containerRect,\n          // Pass mutable reference to onPointerMissed so it's free to update\n          onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n          onCreated: state => {\n            // Connect to event source\n            state.events.connect == null ? void 0 : state.events.connect(eventSource ? (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.i)(eventSource) ? eventSource.current : eventSource : divRef.current);\n            // Set up compute function\n            if (eventPrefix) {\n              state.setEvents({\n                compute: (event, state) => {\n                  const x = event[eventPrefix + 'X'];\n                  const y = event[eventPrefix + 'Y'];\n                  state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                  state.raycaster.setFromCamera(state.pointer, state.camera);\n                }\n              });\n            }\n            // Call onCreated callback\n            onCreated == null ? void 0 : onCreated(state);\n          }\n        });\n        root.current.render( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Bridge, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.E, {\n            set: setError,\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n              fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.B, {\n                set: setBlock\n              }),\n              children: children != null ? children : null\n            })\n          })\n        }));\n      }\n      run();\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n}\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nfunction Canvas(props) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(its_fine__WEBPACK_IMPORTED_MODULE_8__.FiberProvider, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(CanvasImpl, {\n      ...props\n    })\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2ZpYmVyL2Rpc3QvcmVhY3QtdGhyZWUtZmliZXIuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBb1A7QUFDa1E7QUFDdmQ7QUFDQTtBQUNZO0FBQ0Y7QUFDRDtBQUNKO0FBQ1A7QUFDSDtBQUNQO0FBQ0k7O0FBRXZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxzREFBbUI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsRUFBRSwwQ0FBYSxPQUFPLDBEQUFNLENBQUMsa0NBQUs7QUFDbEMsaUJBQWlCLDBEQUFTO0FBQzFCLHdDQUF3Qyw2REFBVTtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSCxvQkFBb0IseUNBQVk7QUFDaEMsaUJBQWlCLHlDQUFZO0FBQzdCLEVBQUUsc0RBQXlCO0FBQzNCLDhCQUE4QiwwREFBa0I7QUFDaEQsNEJBQTRCLDJDQUFjO0FBQzFDLDRCQUE0QiwyQ0FBYzs7QUFFMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHlDQUFZO0FBQzNCLEVBQUUsMERBQXlCO0FBQzNCO0FBQ0E7QUFDQSx3Q0FBd0MsMERBQVU7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVGQUF1RiwwREFBSztBQUM1RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsMENBQTBDLHNEQUFHO0FBQzdDLGlDQUFpQyxzREFBRyxDQUFDLHNEQUFhO0FBQ2xEO0FBQ0EsbUNBQW1DLHNEQUFHLENBQUMsMkNBQWM7QUFDckQscUNBQXFDLHNEQUFHLENBQUMsc0RBQUs7QUFDOUM7QUFDQSxlQUFlO0FBQ2Y7QUFDQSxhQUFhO0FBQ2IsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSw0Q0FBZTtBQUNqQjtBQUNBLDZCQUE2QiwwREFBc0I7QUFDbkQsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isc0RBQUc7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLDJCQUEyQixzREFBRztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCw2QkFBNkIsc0RBQUc7QUFDaEM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixzREFBRyxDQUFDLG1EQUFhO0FBQ3ZDLDJCQUEyQixzREFBRztBQUM5QjtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7O0FBRWtCIiwic291cmNlcyI6WyIvVXNlcnMvc2FudGhvc2hwaXRjaGFpL0Rlc2t0b3AvU2FhU2lmeXgvbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9maWJlci9kaXN0L3JlYWN0LXRocmVlLWZpYmVyLmVzbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBlIGFzIGV4dGVuZCwgdSBhcyB1c2VCcmlkZ2UsIGEgYXMgdXNlTXV0YWJsZUNhbGxiYWNrLCBiIGFzIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QsIGMgYXMgY3JlYXRlUm9vdCwgaSBhcyBpc1JlZiwgRSBhcyBFcnJvckJvdW5kYXJ5LCBCIGFzIEJsb2NrLCBkIGFzIHVubW91bnRDb21wb25lbnRBdE5vZGUsIGYgYXMgY3JlYXRlUG9pbnRlckV2ZW50cyB9IGZyb20gJy4vZXZlbnRzLWRjNDRjMWI4LmVzbS5qcyc7XG5leHBvcnQgeyB0IGFzIFJlYWN0VGhyZWVGaWJlciwgXyBhcyBfcm9vdHMsIHcgYXMgYWN0LCBrIGFzIGFkZEFmdGVyRWZmZWN0LCBqIGFzIGFkZEVmZmVjdCwgbCBhcyBhZGRUYWlsLCBuIGFzIGFkdmFuY2UsIHEgYXMgYXBwbHlQcm9wcywgeCBhcyBidWlsZEdyYXBoLCBwIGFzIGNvbnRleHQsIGcgYXMgY3JlYXRlRXZlbnRzLCBvIGFzIGNyZWF0ZVBvcnRhbCwgYyBhcyBjcmVhdGVSb290LCB2IGFzIGRpc3Bvc2UsIGYgYXMgZXZlbnRzLCBlIGFzIGV4dGVuZCwgaCBhcyBmbHVzaEdsb2JhbEVmZmVjdHMsIHMgYXMgZ2V0Um9vdFN0YXRlLCBtIGFzIGludmFsaWRhdGUsIHIgYXMgcmVjb25jaWxlciwgZCBhcyB1bm1vdW50Q29tcG9uZW50QXROb2RlLCBDIGFzIHVzZUZyYW1lLCBEIGFzIHVzZUdyYXBoLCB5IGFzIHVzZUluc3RhbmNlSGFuZGxlLCBGIGFzIHVzZUxvYWRlciwgeiBhcyB1c2VTdG9yZSwgQSBhcyB1c2VUaHJlZSB9IGZyb20gJy4vZXZlbnRzLWRjNDRjMWI4LmVzbS5qcyc7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgKiBhcyBUSFJFRSBmcm9tICd0aHJlZSc7XG5pbXBvcnQgdXNlTWVhc3VyZSBmcm9tICdyZWFjdC11c2UtbWVhc3VyZSc7XG5pbXBvcnQgeyBGaWJlclByb3ZpZGVyIH0gZnJvbSAnaXRzLWZpbmUnO1xuaW1wb3J0IHsganN4IH0gZnJvbSAncmVhY3QvanN4LXJ1bnRpbWUnO1xuaW1wb3J0ICdyZWFjdC1yZWNvbmNpbGVyL2NvbnN0YW50cyc7XG5pbXBvcnQgJ3p1c3RhbmQvdHJhZGl0aW9uYWwnO1xuaW1wb3J0ICdyZWFjdC1yZWNvbmNpbGVyJztcbmltcG9ydCAnc2NoZWR1bGVyJztcbmltcG9ydCAnc3VzcGVuZC1yZWFjdCc7XG5cbmZ1bmN0aW9uIENhbnZhc0ltcGwoe1xuICByZWYsXG4gIGNoaWxkcmVuLFxuICBmYWxsYmFjayxcbiAgcmVzaXplLFxuICBzdHlsZSxcbiAgZ2wsXG4gIGV2ZW50cyA9IGNyZWF0ZVBvaW50ZXJFdmVudHMsXG4gIGV2ZW50U291cmNlLFxuICBldmVudFByZWZpeCxcbiAgc2hhZG93cyxcbiAgbGluZWFyLFxuICBmbGF0LFxuICBsZWdhY3ksXG4gIG9ydGhvZ3JhcGhpYyxcbiAgZnJhbWVsb29wLFxuICBkcHIsXG4gIHBlcmZvcm1hbmNlLFxuICByYXljYXN0ZXIsXG4gIGNhbWVyYSxcbiAgc2NlbmUsXG4gIG9uUG9pbnRlck1pc3NlZCxcbiAgb25DcmVhdGVkLFxuICAuLi5wcm9wc1xufSkge1xuICAvLyBDcmVhdGUgYSBrbm93biBjYXRhbG9ndWUgb2YgVGhyZWVqcy1uYXRpdmUgZWxlbWVudHNcbiAgLy8gVGhpcyB3aWxsIGluY2x1ZGUgdGhlIGVudGlyZSBUSFJFRSBuYW1lc3BhY2UgYnkgZGVmYXVsdCwgdXNlcnMgY2FuIGV4dGVuZFxuICAvLyB0aGVpciBvd24gZWxlbWVudHMgYnkgdXNpbmcgdGhlIGNyZWF0ZVJvb3QgQVBJIGluc3RlYWRcbiAgUmVhY3QudXNlTWVtbygoKSA9PiBleHRlbmQoVEhSRUUpLCBbXSk7XG4gIGNvbnN0IEJyaWRnZSA9IHVzZUJyaWRnZSgpO1xuICBjb25zdCBbY29udGFpbmVyUmVmLCBjb250YWluZXJSZWN0XSA9IHVzZU1lYXN1cmUoe1xuICAgIHNjcm9sbDogdHJ1ZSxcbiAgICBkZWJvdW5jZToge1xuICAgICAgc2Nyb2xsOiA1MCxcbiAgICAgIHJlc2l6ZTogMFxuICAgIH0sXG4gICAgLi4ucmVzaXplXG4gIH0pO1xuICBjb25zdCBjYW52YXNSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gIGNvbnN0IGRpdlJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgUmVhY3QudXNlSW1wZXJhdGl2ZUhhbmRsZShyZWYsICgpID0+IGNhbnZhc1JlZi5jdXJyZW50KTtcbiAgY29uc3QgaGFuZGxlUG9pbnRlck1pc3NlZCA9IHVzZU11dGFibGVDYWxsYmFjayhvblBvaW50ZXJNaXNzZWQpO1xuICBjb25zdCBbYmxvY2ssIHNldEJsb2NrXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gU3VzcGVuZCB0aGlzIGNvbXBvbmVudCBpZiBibG9jayBpcyBhIHByb21pc2UgKDJuZCBydW4pXG4gIGlmIChibG9jaykgdGhyb3cgYmxvY2s7XG4gIC8vIFRocm93IGV4Y2VwdGlvbiBvdXR3YXJkcyBpZiBhbnl0aGluZyB3aXRoaW4gY2FudmFzIHRocm93c1xuICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuICBjb25zdCByb290ID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjYW52YXMgPSBjYW52YXNSZWYuY3VycmVudDtcbiAgICBpZiAoY29udGFpbmVyUmVjdC53aWR0aCA+IDAgJiYgY29udGFpbmVyUmVjdC5oZWlnaHQgPiAwICYmIGNhbnZhcykge1xuICAgICAgaWYgKCFyb290LmN1cnJlbnQpIHJvb3QuY3VycmVudCA9IGNyZWF0ZVJvb3QoY2FudmFzKTtcbiAgICAgIGFzeW5jIGZ1bmN0aW9uIHJ1bigpIHtcbiAgICAgICAgYXdhaXQgcm9vdC5jdXJyZW50LmNvbmZpZ3VyZSh7XG4gICAgICAgICAgZ2wsXG4gICAgICAgICAgc2NlbmUsXG4gICAgICAgICAgZXZlbnRzLFxuICAgICAgICAgIHNoYWRvd3MsXG4gICAgICAgICAgbGluZWFyLFxuICAgICAgICAgIGZsYXQsXG4gICAgICAgICAgbGVnYWN5LFxuICAgICAgICAgIG9ydGhvZ3JhcGhpYyxcbiAgICAgICAgICBmcmFtZWxvb3AsXG4gICAgICAgICAgZHByLFxuICAgICAgICAgIHBlcmZvcm1hbmNlLFxuICAgICAgICAgIHJheWNhc3RlcixcbiAgICAgICAgICBjYW1lcmEsXG4gICAgICAgICAgc2l6ZTogY29udGFpbmVyUmVjdCxcbiAgICAgICAgICAvLyBQYXNzIG11dGFibGUgcmVmZXJlbmNlIHRvIG9uUG9pbnRlck1pc3NlZCBzbyBpdCdzIGZyZWUgdG8gdXBkYXRlXG4gICAgICAgICAgb25Qb2ludGVyTWlzc2VkOiAoLi4uYXJncykgPT4gaGFuZGxlUG9pbnRlck1pc3NlZC5jdXJyZW50ID09IG51bGwgPyB2b2lkIDAgOiBoYW5kbGVQb2ludGVyTWlzc2VkLmN1cnJlbnQoLi4uYXJncyksXG4gICAgICAgICAgb25DcmVhdGVkOiBzdGF0ZSA9PiB7XG4gICAgICAgICAgICAvLyBDb25uZWN0IHRvIGV2ZW50IHNvdXJjZVxuICAgICAgICAgICAgc3RhdGUuZXZlbnRzLmNvbm5lY3QgPT0gbnVsbCA/IHZvaWQgMCA6IHN0YXRlLmV2ZW50cy5jb25uZWN0KGV2ZW50U291cmNlID8gaXNSZWYoZXZlbnRTb3VyY2UpID8gZXZlbnRTb3VyY2UuY3VycmVudCA6IGV2ZW50U291cmNlIDogZGl2UmVmLmN1cnJlbnQpO1xuICAgICAgICAgICAgLy8gU2V0IHVwIGNvbXB1dGUgZnVuY3Rpb25cbiAgICAgICAgICAgIGlmIChldmVudFByZWZpeCkge1xuICAgICAgICAgICAgICBzdGF0ZS5zZXRFdmVudHMoe1xuICAgICAgICAgICAgICAgIGNvbXB1dGU6IChldmVudCwgc3RhdGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHggPSBldmVudFtldmVudFByZWZpeCArICdYJ107XG4gICAgICAgICAgICAgICAgICBjb25zdCB5ID0gZXZlbnRbZXZlbnRQcmVmaXggKyAnWSddO1xuICAgICAgICAgICAgICAgICAgc3RhdGUucG9pbnRlci5zZXQoeCAvIHN0YXRlLnNpemUud2lkdGggKiAyIC0gMSwgLSh5IC8gc3RhdGUuc2l6ZS5oZWlnaHQpICogMiArIDEpO1xuICAgICAgICAgICAgICAgICAgc3RhdGUucmF5Y2FzdGVyLnNldEZyb21DYW1lcmEoc3RhdGUucG9pbnRlciwgc3RhdGUuY2FtZXJhKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gQ2FsbCBvbkNyZWF0ZWQgY2FsbGJhY2tcbiAgICAgICAgICAgIG9uQ3JlYXRlZCA9PSBudWxsID8gdm9pZCAwIDogb25DcmVhdGVkKHN0YXRlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICByb290LmN1cnJlbnQucmVuZGVyKCAvKiNfX1BVUkVfXyovanN4KEJyaWRnZSwge1xuICAgICAgICAgIGNoaWxkcmVuOiAvKiNfX1BVUkVfXyovanN4KEVycm9yQm91bmRhcnksIHtcbiAgICAgICAgICAgIHNldDogc2V0RXJyb3IsXG4gICAgICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qL2pzeChSZWFjdC5TdXNwZW5zZSwge1xuICAgICAgICAgICAgICBmYWxsYmFjazogLyojX19QVVJFX18qL2pzeChCbG9jaywge1xuICAgICAgICAgICAgICAgIHNldDogc2V0QmxvY2tcbiAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgIGNoaWxkcmVuOiBjaGlsZHJlbiAhPSBudWxsID8gY2hpbGRyZW4gOiBudWxsXG4gICAgICAgICAgICB9KVxuICAgICAgICAgIH0pXG4gICAgICAgIH0pKTtcbiAgICAgIH1cbiAgICAgIHJ1bigpO1xuICAgIH1cbiAgfSk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY2FudmFzID0gY2FudmFzUmVmLmN1cnJlbnQ7XG4gICAgaWYgKGNhbnZhcykgcmV0dXJuICgpID0+IHVubW91bnRDb21wb25lbnRBdE5vZGUoY2FudmFzKTtcbiAgfSwgW10pO1xuXG4gIC8vIFdoZW4gdGhlIGV2ZW50IHNvdXJjZSBpcyBub3QgdGhpcyBkaXYsIHdlIG5lZWQgdG8gc2V0IHBvaW50ZXItZXZlbnRzIHRvIG5vbmVcbiAgLy8gT3IgZWxzZSB0aGUgY2FudmFzIHdpbGwgYmxvY2sgZXZlbnRzIGZyb20gcmVhY2hpbmcgdGhlIGV2ZW50IHNvdXJjZVxuICBjb25zdCBwb2ludGVyRXZlbnRzID0gZXZlbnRTb3VyY2UgPyAnbm9uZScgOiAnYXV0byc7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovanN4KFwiZGl2XCIsIHtcbiAgICByZWY6IGRpdlJlZixcbiAgICBzdHlsZToge1xuICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgaGVpZ2h0OiAnMTAwJScsXG4gICAgICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gICAgICBwb2ludGVyRXZlbnRzLFxuICAgICAgLi4uc3R5bGVcbiAgICB9LFxuICAgIC4uLnByb3BzLFxuICAgIGNoaWxkcmVuOiAvKiNfX1BVUkVfXyovanN4KFwiZGl2XCIsIHtcbiAgICAgIHJlZjogY29udGFpbmVyUmVmLFxuICAgICAgc3R5bGU6IHtcbiAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgaGVpZ2h0OiAnMTAwJSdcbiAgICAgIH0sXG4gICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qL2pzeChcImNhbnZhc1wiLCB7XG4gICAgICAgIHJlZjogY2FudmFzUmVmLFxuICAgICAgICBzdHlsZToge1xuICAgICAgICAgIGRpc3BsYXk6ICdibG9jaydcbiAgICAgICAgfSxcbiAgICAgICAgY2hpbGRyZW46IGZhbGxiYWNrXG4gICAgICB9KVxuICAgIH0pXG4gIH0pO1xufVxuXG4vKipcclxuICogQSBET00gY2FudmFzIHdoaWNoIGFjY2VwdHMgdGhyZWVqcyBlbGVtZW50cyBhcyBjaGlsZHJlbi5cclxuICogQHNlZSBodHRwczovL2RvY3MucG1uZC5ycy9yZWFjdC10aHJlZS1maWJlci9hcGkvY2FudmFzXHJcbiAqL1xuZnVuY3Rpb24gQ2FudmFzKHByb3BzKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovanN4KEZpYmVyUHJvdmlkZXIsIHtcbiAgICBjaGlsZHJlbjogLyojX19QVVJFX18qL2pzeChDYW52YXNJbXBsLCB7XG4gICAgICAuLi5wcm9wc1xuICAgIH0pXG4gIH0pO1xufVxuXG5leHBvcnQgeyBDYW52YXMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\n");

/***/ })

};
;