"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/scheduler";
exports.ids = ["vendor-chunks/scheduler"];
exports.modules = {

/***/ "(ssr)/./node_modules/scheduler/cjs/scheduler.development.js":
/*!*************************************************************!*\
  !*** ./node_modules/scheduler/cjs/scheduler.development.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/scheduler/cjs/scheduler.development.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/scheduler/index.js":
/*!*****************************************!*\
  !*** ./node_modules/scheduler/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/scheduler.development.js */ \"(ssr)/./node_modules/scheduler/cjs/scheduler.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc2NoZWR1bGVyL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx5SUFBMEQ7QUFDNUQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9ub2RlX21vZHVsZXMvc2NoZWR1bGVyL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9zY2hlZHVsZXIucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9zY2hlZHVsZXIuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/scheduler/index.js\n");

/***/ })

};
;