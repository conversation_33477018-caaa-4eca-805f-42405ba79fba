/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/docs/page";
exports.ids = ["app/docs/page"];
exports.modules = {

/***/ "(rsc)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5cb385645dba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1Y2IzODU2NDVkYmFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(rsc)/./components/providers/auth-provider.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"SaaS Ideas\",\n    description: \"A platform for SaaS ideas and projects\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                            theme: \"dark\",\n                            position: \"top-right\",\n                            toastOptions: {\n                                style: {\n                                    background: 'rgb(30 41 59)',\n                                    border: '1px solid rgb(51 65 85)',\n                                    color: 'rgb(248 250 252)'\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/layout.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers/auth-provider.tsx":
/*!************************************************!*\
  !*** ./components/providers/auth-provider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/SaaSifyx/components/providers/auth-provider.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/SaaSifyx/components/providers/auth-provider.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/SaaSifyx/components/theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Fpage&page=%2Fdocs%2Fpage&appPaths=%2Fdocs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Fpage.tsx&appDir=%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Fpage&page=%2Fdocs%2Fpage&appPaths=%2Fdocs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Fpage.tsx&appDir=%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/page.tsx */ \"(rsc)/./app/docs/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'docs',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/SaaSifyx/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/docs/page\",\n        pathname: \"/docs\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Fpage&page=%2Fdocs%2Fpage&appPaths=%2Fdocs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Fpage.tsx&appDir=%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/page.tsx */ \"(rsc)/./app/docs/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGc2FudGhvc2hwaXRjaGFpJTJGRGVza3RvcCUyRlNhYVNpZnl4JTJGYXBwJTJGZG9jcyUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBOEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9hcHAvZG9jcy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Fproviders%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Fproviders%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/auth-provider.tsx */ \"(rsc)/./components/providers/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGc2FudGhvc2hwaXRjaGFpJTJGRGVza3RvcCUyRlNhYVNpZnl4JTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZzYW50aG9zaHBpdGNoYWklMkZEZXNrdG9wJTJGU2FhU2lmeXglMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGYXV0aC1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZzYW50aG9zaHBpdGNoYWklMkZEZXNrdG9wJTJGU2FhU2lmeXglMkZjb21wb25lbnRzJTJGdGhlbWUtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnNhbnRob3NocGl0Y2hhaSUyRkRlc2t0b3AlMkZTYWFTaWZ5eCUyRm5vZGVfbW9kdWxlcyUyRnNvbm5lciUyRmRpc3QlMkZpbmRleC5tanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0TEFBcUo7QUFDcko7QUFDQSwwS0FBNkk7QUFDN0k7QUFDQSxvTEFBNEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9jb21wb25lbnRzL3Byb3ZpZGVycy9hdXRoLXByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RlclwiXSAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9ub2RlX21vZHVsZXMvc29ubmVyL2Rpc3QvaW5kZXgubWpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Fproviders%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst sections = [\n    {\n        id: \"getting-started\",\n        label: \"Getting Started\"\n    },\n    {\n        id: \"ai-insights\",\n        label: \"AI Insights\"\n    },\n    {\n        id: \"idea-validator\",\n        label: \"AI Idea Validator\"\n    },\n    {\n        id: \"settings\",\n        label: \"Settings\"\n    },\n    {\n        id: \"support\",\n        label: \"Support\"\n    },\n    {\n        id: \"api\",\n        label: \"API\"\n    },\n    {\n        id: \"faq\",\n        label: \"FAQ\"\n    }\n];\nfunction DocsPage() {\n    const [active, setActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"getting-started\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"w-64 bg-slate-900/80 border-r border-slate-800 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white mb-6\",\n                        children: \"Documentation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActive(section.id),\n                                className: `block w-full text-left px-3 py-2 rounded-lg font-medium transition-colors ${active === section.id ? 'bg-blue-600 text-white' : 'text-slate-300 hover:bg-slate-800'}`,\n                                children: section.label\n                            }, section.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-10\",\n                children: [\n                    active === \"getting-started\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"Getting Started\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"text-slate-300 list-decimal ml-6 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Sign up or sign in to your SaaSifyX account.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Navigate using the sidebar: Dashboard, AI Insights, AI Idea Validator, Settings, Documentation, and Support.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Use the \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"AI Idea Validator\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 27\n                                            }, this),\n                                            \" to validate your SaaS ideas with market, technical, and business analysis.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Explore \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"AI Insights\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 27\n                                            }, this),\n                                            \" for project analytics and recommendations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Manage your profile and preferences in \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 58\n                                            }, this),\n                                            \" (theme, notifications, language, etc).\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Access \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 26\n                                            }, this),\n                                            \" and \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 51\n                                            }, this),\n                                            \" for help and FAQs.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-2 text-white\",\n                                        children: \"Main Components\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Overview of your projects and quick actions.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"AI Insights\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Advanced analytics and recommendations for your SaaS projects.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"AI Idea Validator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 50,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Validate new SaaS ideas with AI-powered analysis.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Manage your profile, theme, notifications, and account.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Guides, API reference, and FAQs.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Contact form and help resources.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    active === \"ai-insights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"AI Insights\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"AI Insights provides analytics and actionable recommendations for your SaaS projects. Use it to:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"View project metrics and trends.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Get AI-powered suggestions for growth and optimization.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Track user engagement and feature usage.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Identify potential issues and opportunities.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-slate-400\",\n                                children: [\n                                    \"Navigate to \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                        children: \"AI Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 62\n                                    }, this),\n                                    \" from the sidebar. Select a project to see detailed analytics and recommendations.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    active === \"idea-validator\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"AI Idea Validator\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"The AI Idea Validator helps you assess new SaaS ideas before you build. It provides:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Market Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Size, competition, and demand.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Technical Feasibility\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Complexity and resource needs.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Business Viability\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Revenue projections and validation.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Strategic Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Next steps and actionable insights.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-slate-400\",\n                                children: [\n                                    \"To use: Go to \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                        children: \"AI Idea Validator\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 64\n                                    }, this),\n                                    ' in the sidebar, click \"New Validation\", and fill in your idea details.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    active === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"The Settings page lets you personalize your SaaSifyX experience:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Edit your profile information (name, email, avatar).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Switch between dark, light, or system theme.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Enable or disable email notifications.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Select your preferred language (future feature).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Change your password (stub in demo).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Delete your account (stub in demo).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-slate-400\",\n                                children: \"All changes are saved instantly. Some features require a real backend connection.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    active === \"support\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"Support\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"Need help? The Support page offers:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A contact form to reach our team directly.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Support email: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"mailto:<EMAIL>\",\n                                                className: \"text-blue-400 underline\",\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 34\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Frequently Asked Questions (FAQ).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Links to documentation and resources.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-slate-400\",\n                                children: \"For urgent issues, email us directly. For common questions, check the FAQ section.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    active === \"api\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"API Reference\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"SaaSifyX provides a RESTful API for project and idea management. (API endpoints are for demonstration only.)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"POST /api/ideas\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Submit a new SaaS idea for validation.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"GET /api/ideas\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": List your validated ideas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"GET /api/projects\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": List your SaaS projects.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"PATCH /api/user\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Update user profile and preferences.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"GET /api/insights\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Get analytics and recommendations for your projects.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                        children: \"Note:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" API requires authentication. Use your account token in the \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"Authorization\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 87\n                                    }, this),\n                                    \" header.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    active === \"faq\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I change the theme?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 52\n                                            }, this),\n                                            \"Go to Settings > Theme and select Dark, Light, or System. The UI will update instantly.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I change my password?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 54\n                                            }, this),\n                                            'Go to Settings and click \"Change Password\". (In this demo, this is a stub. In production, you\\'ll receive a password reset email.)'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I delete my account?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 53\n                                            }, this),\n                                            'Go to Settings and click \"Delete Account\". (In this demo, this is a stub. In production, your account and data will be removed.)'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I validate a SaaS idea?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 56\n                                            }, this),\n                                            \"Use the AI Idea Validator from the sidebar, fill in your idea details, and get instant analysis.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I contact support?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 51\n                                            }, this),\n                                            \"Go to the Support page and use the contact form <NAME_EMAIL>.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I navigate the app?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 52\n                                            }, this),\n                                            \"Use the sidebar for all main sections. The top bar provides quick access to your profile and notifications.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I enable or disable notifications?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 67\n                                            }, this),\n                                            \"Go to Settings and use the notifications toggle. (In this demo, this is a UI-only feature.)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Can I change the language?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 52\n                                            }, this),\n                                            \"A language selector is available in Settings. More languages will be supported soon.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Does SaaSifyX integrate with other tools?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 67\n                                            }, this),\n                                            \"Integrations are planned for future releases. Stay tuned for updates!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"I'm having trouble logging in or using a feature. What should I do?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 93\n                                            }, this),\n                                            \"First, try refreshing the page or clearing your browser cache. If the issue persists, contact support.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/docs/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/auth-provider.tsx":
/*!************************************************!*\
  !*** ./components/providers/auth-provider.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n// Create the Auth context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    isLoading: true,\n    signOut: async ()=>{}\n});\n// Export the Auth provider component\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize Supabase client\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)(\"https://svnoufgtafdxrlynhztr.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.yxCYBR8EqZc_dIbeiJg6ZFQs5BheQ-tpMKb9j9FGL5I\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Function to get the initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        const { data: { session } } = await supabase.auth.getSession();\n                        setUser(session?.user || null);\n                    } catch (error) {\n                        console.error(\"Error getting session:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            // Get the initial session\n            getInitialSession();\n            // Set up auth state change listener\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (_event, session)=>{\n                    setUser(session?.user || null);\n                    setIsLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Clean up the subscription when the component unmounts\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        supabase\n    ]);\n    // Sign out function\n    const signOut = async ()=>{\n        await supabase.auth.signOut();\n    };\n    // Provide the auth context to children\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isLoading,\n            signOut\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/providers/auth-provider.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n// Export a hook to use the auth context\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQge1xuICBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlcixcbiAgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMsXG59IGZyb20gJ25leHQtdGhlbWVzJ1xuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/page.tsx */ \"(ssr)/./app/docs/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGc2FudGhvc2hwaXRjaGFpJTJGRGVza3RvcCUyRlNhYVNpZnl4JTJGYXBwJTJGZG9jcyUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBOEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9hcHAvZG9jcy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Fproviders%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Fproviders%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/auth-provider.tsx */ \"(ssr)/./components/providers/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGc2FudGhvc2hwaXRjaGFpJTJGRGVza3RvcCUyRlNhYVNpZnl4JTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZzYW50aG9zaHBpdGNoYWklMkZEZXNrdG9wJTJGU2FhU2lmeXglMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGYXV0aC1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZzYW50aG9zaHBpdGNoYWklMkZEZXNrdG9wJTJGU2FhU2lmeXglMkZjb21wb25lbnRzJTJGdGhlbWUtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnNhbnRob3NocGl0Y2hhaSUyRkRlc2t0b3AlMkZTYWFTaWZ5eCUyRm5vZGVfbW9kdWxlcyUyRnNvbm5lciUyRmRpc3QlMkZpbmRleC5tanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0TEFBcUo7QUFDcko7QUFDQSwwS0FBNkk7QUFDN0k7QUFDQSxvTEFBNEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9jb21wb25lbnRzL3Byb3ZpZGVycy9hdXRoLXByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RlclwiXSAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9ub2RlX21vZHVsZXMvc29ubmVyL2Rpc3QvaW5kZXgubWpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Fproviders%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/sonner","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/next-themes","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Fpage&page=%2Fdocs%2Fpage&appPaths=%2Fdocs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Fpage.tsx&appDir=%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();