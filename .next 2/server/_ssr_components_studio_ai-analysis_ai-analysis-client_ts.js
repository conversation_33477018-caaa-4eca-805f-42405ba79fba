"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_studio_ai-analysis_ai-analysis-client_ts";
exports.ids = ["_ssr_components_studio_ai-analysis_ai-analysis-client_ts"];
exports.modules = {

/***/ "(ssr)/./components/studio/ai-analysis/ai-analysis-client.ts":
/*!*************************************************************!*\
  !*** ./components/studio/ai-analysis/ai-analysis-client.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserProjectsClient: () => (/* binding */ getUserProjectsClient),\n/* harmony export */   saveProjectAnalysisClient: () => (/* binding */ saveProjectAnalysisClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ saveProjectAnalysisClient,getUserProjectsClient auto */ \n// Initialize Supabase client\nconst getSupabaseClient = ()=>{\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://svnoufgtafdxrlynhztr.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN2bm91Zmd0YWZkeHJseW5oenRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzMjYyMTMsImV4cCI6MjA2NDkwMjIxM30.yxCYBR8EqZc_dIbeiJg6ZFQs5BheQ-tpMKb9j9FGL5I\");\n};\n// Save a project analysis to the database\nasync function saveProjectAnalysisClient(analysis) {\n    try {\n        console.log(\"Saving project analysis to database...\");\n        const supabase = getSupabaseClient();\n        // Get current user session\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n        if (sessionError) {\n            console.error(\"Error getting session:\", sessionError);\n            return null;\n        }\n        if (!session?.user) {\n            console.log(\"No authenticated user found, cannot save analysis\");\n            return null;\n        }\n        const userId = session.user.id;\n        console.log(\"User authenticated, ID:\", userId);\n        // Create project data\n        const projectData = {\n            user_id: userId,\n            project_name: analysis.projectName,\n            project_description: analysis.projectDescription,\n            market_feasibility: analysis.marketFeasibility,\n            suggested_improvements: analysis.suggestedImprovements,\n            core_features: analysis.coreFeatures,\n            technical_requirements: analysis.technicalRequirements,\n            pricing_model: analysis.pricingModel,\n            progress: 25 // Initial progress\n        };\n        console.log(\"Inserting project data into database...\");\n        // Insert into database\n        const { data, error } = await supabase.from('projects').insert(projectData).select('id').single();\n        if (error) {\n            console.error('Error saving project analysis:', error);\n            return null;\n        }\n        if (!data) {\n            console.error('Error saving project analysis: No data returned after insert.');\n            return null;\n        }\n        console.log(\"Project saved successfully with ID:\", data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error in saveProjectAnalysisClient:', error);\n        return null;\n    }\n}\n// Get all projects for the current user\nasync function getUserProjectsClient() {\n    try {\n        console.log(\"Fetching user projects from database...\");\n        const supabase = getSupabaseClient();\n        // Get current user session\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n        if (sessionError) {\n            console.error('Error getting user session:', sessionError);\n            return [];\n        }\n        if (!session?.user) {\n            console.log('No authenticated user found, returning empty projects array');\n            return [];\n        }\n        const userId = session.user.id;\n        console.log(\"User authenticated, fetching projects for user ID:\", userId);\n        // Fetch projects for the user\n        const { data, error } = await supabase.from('projects').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error fetching user projects:', error);\n            return [];\n        }\n        console.log(`Found ${data.length} projects for user`);\n        // Transform to AIAnalysisResult interface\n        return data.map((item)=>({\n                id: item.id,\n                projectName: item.project_name,\n                projectDescription: item.project_description,\n                marketFeasibility: item.market_feasibility,\n                suggestedImprovements: item.suggested_improvements,\n                coreFeatures: item.core_features,\n                technicalRequirements: item.technical_requirements,\n                pricingModel: item.pricing_model || []\n            }));\n    } catch (error) {\n        console.error('Error in getUserProjectsClient:', error);\n        return [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/studio/ai-analysis/ai-analysis-client.ts\n");

/***/ })

};
;