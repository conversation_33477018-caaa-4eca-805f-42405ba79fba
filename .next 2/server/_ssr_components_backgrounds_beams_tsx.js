"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_backgrounds_beams_tsx";
exports.ids = ["_ssr_components_backgrounds_beams_tsx"];
exports.modules = {

/***/ "(ssr)/./components/backgrounds/beams.tsx":
/*!******************************************!*\
  !*** ./components/backgrounds/beams.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/PerspectiveCamera.js\");\n/* harmony import */ var three_src_math_MathUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three/src/math/MathUtils.js */ \"(ssr)/./node_modules/three/src/math/MathUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction extendMaterial(BaseMaterial, cfg) {\n    const physical = three__WEBPACK_IMPORTED_MODULE_2__.ShaderLib.physical;\n    const { vertexShader: baseVert, fragmentShader: baseFrag, uniforms: baseUniforms } = physical;\n    const baseDefines = physical.defines ?? {};\n    const uniforms = three__WEBPACK_IMPORTED_MODULE_3__.UniformsUtils.clone(baseUniforms);\n    const defaults = new BaseMaterial(cfg.material || {});\n    if (defaults.color) uniforms.diffuse.value = defaults.color;\n    if (\"roughness\" in defaults) uniforms.roughness.value = defaults.roughness;\n    if (\"metalness\" in defaults) uniforms.metalness.value = defaults.metalness;\n    if (\"envMap\" in defaults) uniforms.envMap.value = defaults.envMap;\n    if (\"envMapIntensity\" in defaults) uniforms.envMapIntensity.value = defaults.envMapIntensity;\n    Object.entries(cfg.uniforms ?? {}).forEach(([key, u])=>{\n        uniforms[key] = u !== null && typeof u === \"object\" && \"value\" in u ? u : {\n            value: u\n        };\n    });\n    let vert = `${cfg.header}\\n${cfg.vertexHeader ?? \"\"}\\n${baseVert}`;\n    let frag = `${cfg.header}\\n${cfg.fragmentHeader ?? \"\"}\\n${baseFrag}`;\n    for (const [inc, code] of Object.entries(cfg.vertex ?? {})){\n        vert = vert.replace(inc, `${inc}\\n${code}`);\n    }\n    for (const [inc, code] of Object.entries(cfg.fragment ?? {})){\n        frag = frag.replace(inc, `${inc}\\n${code}`);\n    }\n    const mat = new three__WEBPACK_IMPORTED_MODULE_3__.ShaderMaterial({\n        defines: {\n            ...baseDefines\n        },\n        uniforms,\n        vertexShader: vert,\n        fragmentShader: frag,\n        lights: true,\n        fog: !!cfg.material?.fog\n    });\n    return mat;\n}\nconst CanvasWrapper = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.Canvas, {\n        dpr: [\n            1,\n            2\n        ],\n        frameloop: \"always\",\n        className: \"w-full h-full absolute inset-0 -z-10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined);\nconst hexToNormalizedRGB = (hex)=>{\n    const clean = hex.replace(\"#\", \"\");\n    const r = Number.parseInt(clean.substring(0, 2), 16);\n    const g = Number.parseInt(clean.substring(2, 4), 16);\n    const b = Number.parseInt(clean.substring(4, 6), 16);\n    return [\n        r / 255,\n        g / 255,\n        b / 255\n    ];\n};\nconst noise = `\nfloat random (in vec2 st) {\n    return fract(sin(dot(st.xy,\n                         vec2(12.9898,78.233)))*\n        43758.5453123);\n}\nfloat noise (in vec2 st) {\n    vec2 i = floor(st);\n    vec2 f = fract(st);\n    float a = random(i);\n    float b = random(i + vec2(1.0, 0.0));\n    float c = random(i + vec2(0.0, 1.0));\n    float d = random(i + vec2(1.0, 1.0));\n    vec2 u = f * f * (3.0 - 2.0 * f);\n    return mix(a, b, u.x) +\n           (c - a)* u.y * (1.0 - u.x) +\n           (d - b) * u.x * u.y;\n}\nvec4 permute(vec4 x){return mod(((x*34.0)+1.0)*x, 289.0);}\nvec4 taylorInvSqrt(vec4 r){return 1.79284291400159 - 0.85373472095314 * r;}\nvec3 fade(vec3 t) {return t*t*t*(t*(t*6.0-15.0)+10.0);}\nfloat cnoise(vec3 P){\n  vec3 Pi0 = floor(P);\n  vec3 Pi1 = Pi0 + vec3(1.0);\n  Pi0 = mod(Pi0, 289.0);\n  Pi1 = mod(Pi1, 289.0);\n  vec3 Pf0 = fract(P);\n  vec3 Pf1 = Pf0 - vec3(1.0);\n  vec4 ix = vec4(Pi0.x, Pi1.x, Pi0.x, Pi1.x);\n  vec4 iy = vec4(Pi0.yy, Pi1.yy);\n  vec4 iz0 = Pi0.zzzz;\n  vec4 iz1 = Pi1.zzzz;\n  vec4 ixy = permute(permute(ix) + iy);\n  vec4 ixy0 = permute(ixy + iz0);\n  vec4 ixy1 = permute(ixy + iz1);\n  vec4 gx0 = ixy0 / 7.0;\n  vec4 gy0 = fract(floor(gx0) / 7.0) - 0.5;\n  gx0 = fract(gx0);\n  vec4 gz0 = vec4(0.5) - abs(gx0) - abs(gy0);\n  vec4 sz0 = step(gz0, vec4(0.0));\n  gx0 -= sz0 * (step(0.0, gx0) - 0.5);\n  gy0 -= sz0 * (step(0.0, gy0) - 0.5);\n  vec4 gx1 = ixy1 / 7.0;\n  vec4 gy1 = fract(floor(gx1) / 7.0) - 0.5;\n  gx1 = fract(gx1);\n  vec4 gz1 = vec4(0.5) - abs(gx1) - abs(gy1);\n  vec4 sz1 = step(gz1, vec4(0.0));\n  gx1 -= sz1 * (step(0.0, gx1) - 0.5);\n  gy1 -= sz1 * (step(0.0, gy1) - 0.5);\n  vec3 g000 = vec3(gx0.x,gy0.x,gz0.x);\n  vec3 g100 = vec3(gx0.y,gy0.y,gz0.y);\n  vec3 g010 = vec3(gx0.z,gy0.z,gz0.z);\n  vec3 g110 = vec3(gx0.w,gy0.w,gz0.w);\n  vec3 g001 = vec3(gx1.x,gy1.x,gz1.x);\n  vec3 g101 = vec3(gx1.y,gy1.y,gz1.y);\n  vec3 g011 = vec3(gx1.z,gy1.z,gz1.z);\n  vec3 g111 = vec3(gx1.w,gy1.w,gz1.w);\n  vec4 norm0 = taylorInvSqrt(vec4(dot(g000,g000),dot(g010,g010),dot(g100,g100),dot(g110,g110)));\n  g000 *= norm0.x; g010 *= norm0.y; g100 *= norm0.z; g110 *= norm0.w;\n  vec4 norm1 = taylorInvSqrt(vec4(dot(g001,g001),dot(g011,g011),dot(g101,g101),dot(g111,g111)));\n  g001 *= norm1.x; g011 *= norm1.y; g101 *= norm1.z; g111 *= norm1.w;\n  float n000 = dot(g000, Pf0);\n  float n100 = dot(g100, vec3(Pf1.x,Pf0.yz));\n  float n010 = dot(g010, vec3(Pf0.x,Pf1.y,Pf0.z));\n  float n110 = dot(g110, vec3(Pf1.xy,Pf0.z));\n  float n001 = dot(g001, vec3(Pf0.xy,Pf1.z));\n  float n101 = dot(g101, vec3(Pf1.x,Pf0.y,Pf1.z));\n  float n011 = dot(g011, vec3(Pf0.x,Pf1.yz));\n  float n111 = dot(g111, Pf1);\n  vec3 fade_xyz = fade(Pf0);\n  vec4 n_z = mix(vec4(n000,n100,n010,n110),vec4(n001,n101,n011,n111),fade_xyz.z);\n  vec2 n_yz = mix(n_z.xy,n_z.zw,fade_xyz.y);\n  float n_xyz = mix(n_yz.x,n_yz.y,fade_xyz.x);\n  return 2.2 * n_xyz;\n}\n`;\nconst Beams = ({ beamWidth = 2, beamHeight = 15, beamNumber = 12, lightColor = \"#3b82f6\", speed = 2, noiseIntensity = 1.75, scale = 0.2, rotation = 0 })=>{\n    const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const beamMaterial = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Beams.useMemo[beamMaterial]\": ()=>extendMaterial(three__WEBPACK_IMPORTED_MODULE_3__.MeshStandardMaterial, {\n                header: `\n  varying vec3 vEye;\n  varying float vNoise;\n  varying vec2 vUv;\n  varying vec3 vPosition;\n  uniform float time;\n  uniform float uSpeed;\n  uniform float uNoiseIntensity;\n  uniform float uScale;\n  ${noise}`,\n                vertexHeader: `\n  float getPos(vec3 pos) {\n    vec3 noisePos =\n      vec3(pos.x * 0., pos.y - uv.y, pos.z + time * uSpeed * 3.) * uScale;\n    return cnoise(noisePos);\n  }\n  vec3 getCurrentPos(vec3 pos) {\n    vec3 newpos = pos;\n    newpos.z += getPos(pos);\n    return newpos;\n  }\n  vec3 getNormal(vec3 pos) {\n    vec3 curpos = getCurrentPos(pos);\n    vec3 nextposX = getCurrentPos(pos + vec3(0.01, 0.0, 0.0));\n    vec3 nextposZ = getCurrentPos(pos + vec3(0.0, -0.01, 0.0));\n    vec3 tangentX = normalize(nextposX - curpos);\n    vec3 tangentZ = normalize(nextposZ - curpos);\n    return normalize(cross(tangentZ, tangentX));\n  }`,\n                fragmentHeader: \"\",\n                vertex: {\n                    \"#include <begin_vertex>\": `transformed.z += getPos(transformed.xyz);`,\n                    \"#include <beginnormal_vertex>\": `objectNormal = getNormal(position.xyz);`\n                },\n                fragment: {\n                    \"#include <dithering_fragment>\": `\n    float randomNoise = noise(gl_FragCoord.xy);\n    gl_FragColor.rgb -= randomNoise / 15. * uNoiseIntensity;`\n                },\n                material: {\n                    fog: true\n                },\n                uniforms: {\n                    diffuse: new three__WEBPACK_IMPORTED_MODULE_3__.Color(...hexToNormalizedRGB(\"#000000\")),\n                    time: {\n                        shared: true,\n                        mixed: true,\n                        linked: true,\n                        value: 0\n                    },\n                    roughness: 0.3,\n                    metalness: 0.3,\n                    uSpeed: {\n                        shared: true,\n                        mixed: true,\n                        linked: true,\n                        value: speed\n                    },\n                    envMapIntensity: 10,\n                    uNoiseIntensity: noiseIntensity,\n                    uScale: scale\n                }\n            })\n    }[\"Beams.useMemo[beamMaterial]\"], [\n        speed,\n        noiseIntensity,\n        scale\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanvasWrapper, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"group\", {\n                rotation: [\n                    0,\n                    0,\n                    (0,three_src_math_MathUtils_js__WEBPACK_IMPORTED_MODULE_5__.degToRad)(rotation)\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlaneNoise, {\n                        ref: meshRef,\n                        material: beamMaterial,\n                        count: beamNumber,\n                        width: beamWidth,\n                        height: beamHeight\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DirLight, {\n                        color: lightColor,\n                        position: [\n                            0,\n                            3,\n                            10\n                        ]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                intensity: 1\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"color\", {\n                attach: \"background\",\n                args: [\n                    \"#0a0a0a\"\n                ]\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_6__.PerspectiveCamera, {\n                makeDefault: true,\n                position: [\n                    0,\n                    0,\n                    20\n                ],\n                fov: 30\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, undefined);\n};\nfunction createStackedPlanesBufferGeometry(n, width, height, spacing, heightSegments) {\n    const geometry = new three__WEBPACK_IMPORTED_MODULE_3__.BufferGeometry();\n    const numVertices = n * (heightSegments + 1) * 2;\n    const numFaces = n * heightSegments * 2;\n    const positions = new Float32Array(numVertices * 3);\n    const indices = new Uint32Array(numFaces * 3);\n    const uvs = new Float32Array(numVertices * 2);\n    let vertexOffset = 0;\n    let indexOffset = 0;\n    let uvOffset = 0;\n    const totalWidth = n * width + (n - 1) * spacing;\n    const xOffsetBase = -totalWidth / 2;\n    for(let i = 0; i < n; i++){\n        const xOffset = xOffsetBase + i * (width + spacing);\n        const uvXOffset = Math.random() * 300;\n        const uvYOffset = Math.random() * 300;\n        for(let j = 0; j <= heightSegments; j++){\n            const y = height * (j / heightSegments - 0.5);\n            const v0 = [\n                xOffset,\n                y,\n                0\n            ];\n            const v1 = [\n                xOffset + width,\n                y,\n                0\n            ];\n            positions.set([\n                ...v0,\n                ...v1\n            ], vertexOffset * 3);\n            const uvY = j / heightSegments;\n            uvs.set([\n                uvXOffset,\n                uvY + uvYOffset,\n                uvXOffset + 1,\n                uvY + uvYOffset\n            ], uvOffset);\n            if (j < heightSegments) {\n                const a = vertexOffset, b = vertexOffset + 1, c = vertexOffset + 2, d = vertexOffset + 3;\n                indices.set([\n                    a,\n                    b,\n                    c,\n                    c,\n                    b,\n                    d\n                ], indexOffset);\n                indexOffset += 6;\n            }\n            vertexOffset += 2;\n            uvOffset += 4;\n        }\n    }\n    geometry.setAttribute(\"position\", new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(positions, 3));\n    geometry.setAttribute(\"uv\", new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(uvs, 2));\n    geometry.setIndex(new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(indices, 1));\n    geometry.computeVertexNormals();\n    return geometry;\n}\nconst MergedPlanes = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ material, width, count, height }, ref)=>{\n    const mesh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"MergedPlanes.useImperativeHandle\": ()=>mesh.current\n    }[\"MergedPlanes.useImperativeHandle\"]);\n    const geometry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"MergedPlanes.useMemo[geometry]\": ()=>createStackedPlanesBufferGeometry(count, width, height, 0, 100)\n    }[\"MergedPlanes.useMemo[geometry]\"], [\n        count,\n        width,\n        height\n    ]);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_7__.C)({\n        \"MergedPlanes.useFrame\": (_, delta)=>{\n            mesh.current.material.uniforms.time.value += 0.1 * delta;\n        }\n    }[\"MergedPlanes.useFrame\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        ref: mesh,\n        geometry: geometry,\n        material: material\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n        lineNumber: 336,\n        columnNumber: 10\n    }, undefined);\n});\nMergedPlanes.displayName = \"MergedPlanes\";\nconst PlaneNoise = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MergedPlanes, {\n        ref: ref,\n        material: props.material,\n        width: props.width,\n        count: props.count,\n        height: props.height\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n        lineNumber: 349,\n        columnNumber: 3\n    }, undefined));\nPlaneNoise.displayName = \"PlaneNoise\";\nconst DirLight = ({ position, color })=>{\n    const dir = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DirLight.useEffect\": ()=>{\n            if (!dir.current) return;\n            const cam = dir.current.shadow.camera;\n            cam.top = 24;\n            cam.bottom = -24;\n            cam.left = -24;\n            cam.right = 24;\n            cam.far = 64;\n            dir.current.shadow.bias = -0.004;\n        }\n    }[\"DirLight.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"directionalLight\", {\n        ref: dir,\n        color: color,\n        intensity: 1,\n        position: position\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/backgrounds/beams.tsx\",\n        lineNumber: 371,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Beams);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/backgrounds/beams.tsx\n");

/***/ })

};
;