{"app/page.tsx -> @/components/AnimatedLogo": {"id": "app/page.tsx -> @/components/AnimatedLogo", "files": ["static/chunks/_app-pages-browser_components_AnimatedLogo_tsx.js"]}, "app/page.tsx -> @/components/SplashScreen": {"id": "app/page.tsx -> @/components/SplashScreen", "files": ["static/chunks/_app-pages-browser_components_SplashScreen_tsx.js"]}, "components/hero.tsx -> @/components/backgrounds/beams": {"id": "components/hero.tsx -> @/components/backgrounds/beams", "files": ["static/chunks/_app-pages-browser_components_backgrounds_beams_tsx.js"]}, "components/studio/ai-analysis/ai-analysis-modal.tsx -> ./ai-analysis-client": {"id": "components/studio/ai-analysis/ai-analysis-modal.tsx -> ./ai-analysis-client", "files": ["static/chunks/_app-pages-browser_components_studio_ai-analysis_ai-analysis-client_ts.js"]}, "node_modules/@react-three/drei/core/VideoTexture.js -> hls.js": {"id": "node_modules/@react-three/drei/core/VideoTexture.js -> hls.js", "files": []}, "node_modules/@react-three/drei/web/FaceLandmarker.js -> @mediapipe/tasks-vision": {"id": "node_modules/@react-three/drei/web/FaceLandmarker.js -> @mediapipe/tasks-vision", "files": []}, "node_modules/@supabase/auth-js/dist/module/lib/helpers.js -> @supabase/node-fetch": {"id": "node_modules/@supabase/auth-js/dist/module/lib/helpers.js -> @supabase/node-fetch", "files": []}, "node_modules/@supabase/functions-js/dist/module/helper.js -> @supabase/node-fetch": {"id": "node_modules/@supabase/functions-js/dist/module/helper.js -> @supabase/node-fetch", "files": []}, "node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js -> @supabase/node-fetch": {"id": "node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js -> @supabase/node-fetch", "files": []}, "node_modules/@supabase/storage-js/dist/module/lib/helpers.js -> @supabase/node-fetch": {"id": "node_modules/@supabase/storage-js/dist/module/lib/helpers.js -> @supabase/node-fetch", "files": []}}