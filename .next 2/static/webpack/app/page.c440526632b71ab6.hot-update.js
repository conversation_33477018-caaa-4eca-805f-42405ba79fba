"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/RightMediaPanel.tsx":
/*!********************************************!*\
  !*** ./app/components/RightMediaPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RightMediaPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nconst sectionImageMap = [\n    {\n        id: 'features',\n        src: '/images/dashboard.jpg',\n        alt: 'Dashboard Feature'\n    },\n    {\n        id: 'statistics',\n        src: '/images/ai-insights.jpg',\n        alt: 'AI Insights'\n    },\n    {\n        id: 'about',\n        src: '/images/ai-validator.jpg',\n        alt: 'AI Validator'\n    }\n];\nfunction RightMediaPanel() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const observers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RightMediaPanel.useEffect\": ()=>{\n            const handleIntersect = {\n                \"RightMediaPanel.useEffect.handleIntersect\": (entries)=>{\n                    entries.forEach({\n                        \"RightMediaPanel.useEffect.handleIntersect\": (entry)=>{\n                            if (entry.isIntersecting) {\n                                setActiveSection(entry.target.id);\n                            }\n                        }\n                    }[\"RightMediaPanel.useEffect.handleIntersect\"]);\n                }\n            }[\"RightMediaPanel.useEffect.handleIntersect\"];\n            const opts = {\n                threshold: 0.4\n            };\n            observers.current = sectionImageMap.map({\n                \"RightMediaPanel.useEffect\": (param)=>{\n                    let { id } = param;\n                    const el = document.getElementById(id);\n                    if (!el) return null;\n                    const observer = new window.IntersectionObserver(handleIntersect, opts);\n                    observer.observe(el);\n                    return observer;\n                }\n            }[\"RightMediaPanel.useEffect\"]).filter(Boolean);\n            return ({\n                \"RightMediaPanel.useEffect\": ()=>{\n                    observers.current.forEach({\n                        \"RightMediaPanel.useEffect\": (obs)=>obs.disconnect()\n                    }[\"RightMediaPanel.useEffect\"]);\n                }\n            })[\"RightMediaPanel.useEffect\"];\n        }\n    }[\"RightMediaPanel.useEffect\"], []);\n    const current = sectionImageMap.find((s)=>s.id === activeSection);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden lg:block fixed top-32 right-8 w-[340px] h-[480px] z-30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n            mode: \"wait\",\n            children: current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.img, {\n                src: current.src,\n                alt: current.alt,\n                initial: {\n                    opacity: 0,\n                    x: 40\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                exit: {\n                    opacity: 0,\n                    x: 40\n                },\n                transition: {\n                    duration: 0.6,\n                    ease: 'easeOut'\n                },\n                className: \"w-full h-full object-cover rounded-2xl shadow-2xl border border-white/10 bg-white/10 backdrop-blur-xl\",\n                draggable: false\n            }, current.src, false, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/components/RightMediaPanel.tsx\",\n                lineNumber: 41,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/components/RightMediaPanel.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/components/RightMediaPanel.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(RightMediaPanel, \"RoPPGMy082HEyuWgd77yq2N7L78=\");\n_c = RightMediaPanel;\nvar _c;\n$RefreshReg$(_c, \"RightMediaPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/RightMediaPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/hero */ \"(app-pages-browser)/./components/hero.tsx\");\n/* harmony import */ var _components_features__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/features */ \"(app-pages-browser)/./components/features.tsx\");\n/* harmony import */ var _components_call_to_action__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/call-to-action */ \"(app-pages-browser)/./components/call-to-action.tsx\");\n/* harmony import */ var _components_navbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/navbar */ \"(app-pages-browser)/./components/navbar.tsx\");\n/* harmony import */ var _components_footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/footer */ \"(app-pages-browser)/./components/footer.tsx\");\n/* harmony import */ var _components_statistics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/statistics */ \"(app-pages-browser)/./components/statistics.tsx\");\n/* harmony import */ var _components_animations_ScrollReveal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/animations/ScrollReveal */ \"(app-pages-browser)/./components/animations/ScrollReveal.tsx\");\n/* harmony import */ var _components_animations_ParallaxSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/animations/ParallaxSection */ \"(app-pages-browser)/./components/animations/ParallaxSection.tsx\");\n/* harmony import */ var _components_animations_FloatingIcons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/animations/FloatingIcons */ \"(app-pages-browser)/./components/animations/FloatingIcons.tsx\");\n/* harmony import */ var _components_animations_BackgroundGrid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/animations/BackgroundGrid */ \"(app-pages-browser)/./components/animations/BackgroundGrid.tsx\");\n/* harmony import */ var _components_animations_TechyNetworkBG__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/animations/TechyNetworkBG */ \"(app-pages-browser)/./components/animations/TechyNetworkBG.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_RightMediaPanel__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./components/RightMediaPanel */ \"(app-pages-browser)/./app/components/RightMediaPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SplashScreen = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_SplashScreen_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/SplashScreen */ \"(app-pages-browser)/./components/SplashScreen.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app/page.tsx -> \" + \"@/components/SplashScreen\"\n        ]\n    },\n    ssr: false\n});\n_c = SplashScreen;\nconst AnimatedLogo = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(_c1 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_AnimatedLogo_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/AnimatedLogo */ \"(app-pages-browser)/./components/AnimatedLogo.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app/page.tsx -> \" + \"@/components/AnimatedLogo\"\n        ]\n    },\n    ssr: false\n});\n_c2 = AnimatedLogo;\n// Prefers-reduced-motion hook\nconst useReducedMotion = ()=>{\n    _s();\n    const [prefersReduced, setPrefersReduced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useReducedMotion.useEffect\": ()=>{\n            const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');\n            setPrefersReduced(mediaQuery.matches);\n            const onChange = {\n                \"useReducedMotion.useEffect.onChange\": ()=>setPrefersReduced(mediaQuery.matches)\n            }[\"useReducedMotion.useEffect.onChange\"];\n            mediaQuery.addEventListener('change', onChange);\n            return ({\n                \"useReducedMotion.useEffect\": ()=>mediaQuery.removeEventListener('change', onChange)\n            })[\"useReducedMotion.useEffect\"];\n        }\n    }[\"useReducedMotion.useEffect\"], []);\n    return prefersReduced;\n};\n_s(useReducedMotion, \"fYDGaKObzORXViYblRClclmCtLA=\");\nfunction LandingPage() {\n    _s1();\n    const [showSplash, setShowSplash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const prefersReducedMotion = useReducedMotion();\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_15__.useScroll)();\n    // Mouse movement effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (prefersReducedMotion) return;\n            const handleMouseMove = {\n                \"LandingPage.useEffect.handleMouseMove\": (e)=>{\n                    setMousePosition({\n                        x: e.clientX / window.innerWidth,\n                        y: e.clientY / window.innerHeight\n                    });\n                }\n            }[\"LandingPage.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            return ({\n                \"LandingPage.useEffect\": ()=>window.removeEventListener('mousemove', handleMouseMove)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], [\n        prefersReducedMotion\n    ]);\n    // Smooth animations\n    const springConfig = {\n        stiffness: 100,\n        damping: 30,\n        restDelta: 0.001\n    };\n    const smoothProgress = (0,framer_motion__WEBPACK_IMPORTED_MODULE_16__.useSpring)(scrollYProgress, springConfig);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.AnimatePresence, {\n            mode: \"wait\",\n            children: showSplash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SplashScreen, {\n                onComplete: ()=>setShowSplash(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                transition: {\n                    duration: 0.8,\n                    ease: [\n                        0.22,\n                        1,\n                        0.36,\n                        1\n                    ]\n                },\n                className: \"relative min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_BackgroundGrid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this),\n                    !prefersReducedMotion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_FloatingIcons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 39\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RightMediaPanel__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"relative z-10 container mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                initial: {\n                                    y: -20,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    y: 0,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 200,\n                                    damping: 20\n                                },\n                                className: \"py-4 sm:py-6 lg:py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-16 sm:space-y-24 lg:space-y-32\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_ScrollReveal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                            className: \"relative\",\n                                            whileInView: {\n                                                scale: [\n                                                    0.95,\n                                                    1\n                                                ],\n                                                opacity: [\n                                                    0,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                ease: \"easeOut\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_TechyNetworkBG__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"absolute inset-0 z-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_ParallaxSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        speed: 0.3,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_ScrollReveal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                                whileHover: {\n                                                    scale: 1.02,\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_statistics__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_ParallaxSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        speed: 0.2,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            id: \"features\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_FloatingIcons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"top-0 left-0 w-full h-full z-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 max-w-5xl mx-auto px-4 sm:px-8 lg:px-16 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 justify-center\",\n                                                    children: [\n                                                        'AI Analysis',\n                                                        'User Flow Designer',\n                                                        'Smart Kanban Board',\n                                                        'Cursor AI Integration'\n                                                    ].map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_ScrollReveal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            delay: 0.1 + i * 0.1,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                                                whileHover: {\n                                                                    scale: 1.04,\n                                                                    boxShadow: '0 4px 32px 0 rgba(80,80,255,0.10)',\n                                                                    transition: {\n                                                                        type: \"spring\",\n                                                                        stiffness: 300,\n                                                                        damping: 22\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    perspective: 1000\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    feature: feature,\n                                                                    index: i,\n                                                                    mousePosition: mousePosition\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, feature, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        id: \"pricing\",\n                                        className: \"max-w-4xl mx-auto px-4 sm:px-8 lg:px-16 py-24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-center mb-8 gradient-text\",\n                                                children: \"Pricing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-2xl bg-slate-900/70 p-8 shadow-lg border border-slate-800 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2\",\n                                                                children: \"Starter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold mb-4\",\n                                                                children: \"$0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-slate-300 space-y-2 mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Basic AI Analysis\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Community Support\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Limited Projects\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"w-full py-2 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-500 transition\",\n                                                                children: \"Get Started\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-2xl bg-gradient-to-br from-blue-700/80 to-purple-700/80 p-8 shadow-xl border-2 border-blue-500 text-center scale-105\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2 text-white\",\n                                                                children: \"Pro\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold mb-4 text-white\",\n                                                                children: [\n                                                                    \"$29\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-normal\",\n                                                                        children: \"/mo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 187,\n                                                                        columnNumber: 78\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-white space-y-2 mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"All Starter Features\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Unlimited Projects\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Premium AI Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Email Support\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"w-full py-2 rounded-lg bg-white text-blue-700 font-semibold hover:bg-blue-100 transition\",\n                                                                children: \"Start Pro\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-2xl bg-slate-900/70 p-8 shadow-lg border border-slate-800 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2\",\n                                                                children: \"Enterprise\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl font-bold mb-4\",\n                                                                children: \"Custom\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-slate-300 space-y-2 mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"All Pro Features\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 200,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Custom Integrations\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Dedicated Support\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"w-full py-2 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-500 transition\",\n                                                                children: \"Contact Sales\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        id: \"about\",\n                                        className: \"max-w-3xl mx-auto px-4 sm:px-8 lg:px-16 py-24 text-center font-sans\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-display font-bold mb-6 gradient-text\",\n                                                children: \"About SassifyX\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-slate-300 mb-4 font-sans\",\n                                                children: \"SassifyX is an AI-powered platform designed to help entrepreneurs and developers validate, build, and launch successful SaaS products. Our mission is to make SaaS innovation accessible to everyone, with powerful tools, actionable insights, and a supportive community.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 font-sans\",\n                                                children: \"Built by passionate engineers and designers, SassifyX leverages the latest in AI and cloud technology to empower your SaaS journey from idea to launch and beyond.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_ParallaxSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        speed: 0.2,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_ScrollReveal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                                whileInView: {\n                                                    scale: [\n                                                        0.95,\n                                                        1\n                                                    ],\n                                                    opacity: [\n                                                        0,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 0.5\n                                                },\n                                                className: \"relative overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_call_to_action__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20\",\n                                                        initial: {\n                                                            scale: 0,\n                                                            opacity: 0\n                                                        },\n                                                        whileInView: {\n                                                            scale: [\n                                                                1,\n                                                                1.5\n                                                            ],\n                                                            opacity: [\n                                                                0.5,\n                                                                0\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 1,\n                                                            ease: \"easeOut\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_ScrollReveal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/page.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s1(LandingPage, \"JeT31v2FFHZ0pPd8XyGdLt6tO4U=\", false, function() {\n    return [\n        useReducedMotion,\n        framer_motion__WEBPACK_IMPORTED_MODULE_15__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_16__.useSpring\n    ];\n});\n_c3 = LandingPage;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"SplashScreen\");\n$RefreshReg$(_c1, \"AnimatedLogo$dynamic\");\n$RefreshReg$(_c2, \"AnimatedLogo\");\n$RefreshReg$(_c3, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});