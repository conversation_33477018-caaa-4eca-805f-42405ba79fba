"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_AnimatedLogo_tsx"],{

/***/ "(app-pages-browser)/./components/AnimatedLogo.tsx":
/*!*************************************!*\
  !*** ./components/AnimatedLogo.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AnimatedLogo = ()=>{\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedLogo.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AnimatedLogo.useEffect\"], []);\n    if (!mounted) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-[70vh] w-full flex items-center justify-center bg-gradient-to-b from-black to-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 1,\n                ease: \"easeOut\"\n            },\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: \"Sassify\".split('').map((letter, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                className: \"text-7xl font-bold\",\n                                style: {\n                                    color: '#FF5733'\n                                },\n                                whileHover: {\n                                    scale: 1.1,\n                                    color: '#FF3300',\n                                    textShadow: '0 0 8px rgba(255,87,51,0.3)'\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 400,\n                                    damping: 10\n                                },\n                                children: letter\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/AnimatedLogo.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/AnimatedLogo.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"ml-4 relative\",\n                        animate: {\n                            rotateY: [\n                                0,\n                                10,\n                                -10,\n                                0\n                            ],\n                            rotateX: [\n                                0,\n                                5,\n                                -5,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 4,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                            className: \"text-8xl font-black\",\n                            style: {\n                                color: '#FF5733',\n                                display: 'inline-block',\n                                textShadow: '0 0 20px rgba(255,87,51,0.5)',\n                                filter: 'drop-shadow(0 0 10px rgba(255,87,51,0.3))'\n                            },\n                            whileHover: {\n                                scale: 1.2,\n                                textShadow: '0 0 30px rgba(255,87,51,0.8)',\n                                color: '#FF3300'\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 300,\n                                damping: 10\n                            },\n                            children: \"X\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/AnimatedLogo.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/AnimatedLogo.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/AnimatedLogo.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/AnimatedLogo.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/AnimatedLogo.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnimatedLogo, \"LrrVfNW3d1raFE0BNzCTILYmIfo=\");\n_c = AnimatedLogo;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnimatedLogo);\nvar _c;\n$RefreshReg$(_c, \"AnimatedLogo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/AnimatedLogo.tsx\n"));

/***/ })

}]);