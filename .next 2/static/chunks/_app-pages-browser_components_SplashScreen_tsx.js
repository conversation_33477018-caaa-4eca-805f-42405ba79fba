"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_SplashScreen_tsx"],{

/***/ "(app-pages-browser)/./components/SplashScreen.tsx":
/*!*************************************!*\
  !*** ./components/SplashScreen.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst SplashScreen = (param)=>{\n    let { onComplete } = param;\n    _s();\n    const [showFullLogo, setShowFullLogo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [done, setDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SplashScreen.useEffect\": ()=>{\n            // Animate loading bar\n            let frame;\n            let start = null;\n            const duration = 1800; // ms\n            function animateBar(ts) {\n                if (!start) start = ts;\n                const elapsed = ts - start;\n                const pct = Math.min(100, elapsed / duration * 100);\n                setProgress(pct);\n                if (pct < 100) {\n                    frame = requestAnimationFrame(animateBar);\n                } else {\n                    setTimeout({\n                        \"SplashScreen.useEffect.animateBar\": ()=>setDone(true)\n                    }[\"SplashScreen.useEffect.animateBar\"], 350); // short pause at 100%\n                }\n            }\n            frame = requestAnimationFrame(animateBar);\n            // Show full logo after X animation\n            const timer1 = setTimeout({\n                \"SplashScreen.useEffect.timer1\": ()=>setShowFullLogo(true)\n            }[\"SplashScreen.useEffect.timer1\"], 600);\n            return ({\n                \"SplashScreen.useEffect\": ()=>{\n                    clearTimeout(timer1);\n                    cancelAnimationFrame(frame);\n                }\n            })[\"SplashScreen.useEffect\"];\n        }\n    }[\"SplashScreen.useEffect\"], []);\n    // Call onComplete after exit animation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SplashScreen.useEffect\": ()=>{\n            if (done) {\n                const timer = setTimeout({\n                    \"SplashScreen.useEffect.timer\": ()=>onComplete()\n                }[\"SplashScreen.useEffect.timer\"], 500);\n                return ({\n                    \"SplashScreen.useEffect\": ()=>clearTimeout(timer)\n                })[\"SplashScreen.useEffect\"];\n            }\n        }\n    }[\"SplashScreen.useEffect\"], [\n        done,\n        onComplete\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: !done && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: \"fixed inset-0 z-50 flex flex-col items-center justify-center\",\n            style: {\n                background: '#101014'\n            },\n            initial: {\n                opacity: 1,\n                scale: 1\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            exit: {\n                opacity: 0,\n                scale: 1.08\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 z-0 pointer-events-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"100%\",\n                        height: \"100%\",\n                        className: \"w-full h-full\",\n                        style: {\n                            opacity: 0.12\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                    id: \"grokGrid\",\n                                    x1: \"0\",\n                                    y1: \"0\",\n                                    x2: \"1\",\n                                    y2: \"1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"0%\",\n                                            stopColor: \"#7c3aed\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"100%\",\n                                            stopColor: \"#06b6d4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, undefined),\n                            Array.from({\n                                length: 20\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                    x1: i * 60,\n                                    y1: 0,\n                                    x2: i * 60,\n                                    y2: 1200,\n                                    stroke: \"url(#grokGrid)\",\n                                    strokeWidth: \"1\"\n                                }, i, false, {\n                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, undefined)),\n                            Array.from({\n                                length: 20\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                    x1: 0,\n                                    y1: i * 60,\n                                    x2: 1200,\n                                    y2: i * 60,\n                                    stroke: \"url(#grokGrid)\",\n                                    strokeWidth: \"1\"\n                                }, i, false, {\n                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 17\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex items-center mb-10 z-10\",\n                    children: [\n                        showFullLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            className: \"flex\",\n                            children: \"Sassify\".split('').map((letter, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.3,\n                                        delay: index * 0.08,\n                                        ease: \"easeOut\"\n                                    },\n                                    className: \"text-7xl font-bold bg-gradient-to-r from-slate-100 to-zinc-400 text-transparent bg-clip-text drop-shadow-[0_0_16px_#7c3aed]\",\n                                    style: {\n                                        textShadow: '0 0 24px #06b6d4, 0 0 8px #7c3aed'\n                                    },\n                                    children: letter\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                scale: 0,\n                                rotate: -180\n                            },\n                            animate: {\n                                scale: 1,\n                                rotate: 0,\n                                transition: {\n                                    duration: 1,\n                                    type: \"spring\",\n                                    stiffness: 200\n                                }\n                            },\n                            className: \"ml-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n                                className: \"text-8xl font-black inline-block\",\n                                style: {\n                                    background: 'linear-gradient(90deg, #e5e7eb 0%, #a3a3a3 100%)',\n                                    color: 'transparent',\n                                    WebkitBackgroundClip: 'text',\n                                    backgroundClip: 'text',\n                                    textShadow: '0 0 32px #06b6d4, 0 0 16px #7c3aed',\n                                    filter: 'drop-shadow(0 0 24px #06b6d4cc)'\n                                },\n                                animate: {\n                                    scale: [\n                                        1,\n                                        1.15,\n                                        1\n                                    ],\n                                    rotate: [\n                                        0,\n                                        10,\n                                        -10,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    ease: \"easeInOut\",\n                                    times: [\n                                        0,\n                                        0.5,\n                                        1\n                                    ],\n                                    repeat: Infinity\n                                },\n                                children: \"X\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-2 text-lg font-semibold tracking-widest drop-shadow z-10\",\n                    style: {\n                        color: '#e5e7eb',\n                        textShadow: '0 0 8px #06b6d4, 0 0 4px #7c3aed'\n                    },\n                    children: [\n                        Math.round(progress),\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-64 h-3 bg-slate-800/60 rounded-full overflow-hidden shadow-lg z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"h-full rounded-full\",\n                        initial: {\n                            width: 0\n                        },\n                        animate: {\n                            width: \"\".concat(progress, \"%\")\n                        },\n                        transition: {\n                            duration: 0.2,\n                            ease: 'linear'\n                        },\n                        style: {\n                            width: \"\".concat(progress, \"%\"),\n                            background: 'linear-gradient(90deg, #e5e7eb 0%, #a3a3a3 100%)',\n                            boxShadow: '0 0 16px #06b6d4, 0 0 8px #7c3aed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n            lineNumber: 49,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/components/SplashScreen.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SplashScreen, \"vBo17yIvxHZV6bq+QxbZjeykYoA=\");\n_c = SplashScreen;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SplashScreen);\nvar _c;\n$RefreshReg$(_c, \"SplashScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/SplashScreen.tsx\n"));

/***/ })

}]);