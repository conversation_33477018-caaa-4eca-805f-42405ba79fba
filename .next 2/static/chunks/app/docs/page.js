/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/docs/page"],{

/***/ "(app-pages-browser)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst sections = [\n    {\n        id: \"getting-started\",\n        label: \"Getting Started\"\n    },\n    {\n        id: \"ai-insights\",\n        label: \"AI Insights\"\n    },\n    {\n        id: \"idea-validator\",\n        label: \"AI Idea Validator\"\n    },\n    {\n        id: \"settings\",\n        label: \"Settings\"\n    },\n    {\n        id: \"support\",\n        label: \"Support\"\n    },\n    {\n        id: \"api\",\n        label: \"API\"\n    },\n    {\n        id: \"faq\",\n        label: \"FAQ\"\n    }\n];\nfunction DocsPage() {\n    _s();\n    const [active, setActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"getting-started\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"w-64 bg-slate-900/80 border-r border-slate-800 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white mb-6\",\n                        children: \"Documentation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActive(section.id),\n                                className: \"block w-full text-left px-3 py-2 rounded-lg font-medium transition-colors \".concat(active === section.id ? 'bg-blue-600 text-white' : 'text-slate-300 hover:bg-slate-800'),\n                                children: section.label\n                            }, section.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-10\",\n                children: [\n                    active === \"getting-started\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"Getting Started\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"text-slate-300 list-decimal ml-6 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Sign up or sign in to your SaaSifyX account.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Navigate using the sidebar: Dashboard, AI Insights, AI Idea Validator, Settings, Documentation, and Support.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Use the \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"AI Idea Validator\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 27\n                                            }, this),\n                                            \" to validate your SaaS ideas with market, technical, and business analysis.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Explore \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"AI Insights\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 27\n                                            }, this),\n                                            \" for project analytics and recommendations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Manage your profile and preferences in \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 58\n                                            }, this),\n                                            \" (theme, notifications, language, etc).\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Access \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 26\n                                            }, this),\n                                            \" and \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 51\n                                            }, this),\n                                            \" for help and FAQs.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-2 text-white\",\n                                        children: \"Main Components\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Overview of your projects and quick actions.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"AI Insights\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Advanced analytics and recommendations for your SaaS projects.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"AI Idea Validator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 50,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Validate new SaaS ideas with AI-powered analysis.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Manage your profile, theme, notifications, and account.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Guides, API reference, and FAQs.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                        children: \"Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \": Contact form and help resources.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    active === \"ai-insights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"AI Insights\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"AI Insights provides analytics and actionable recommendations for your SaaS projects. Use it to:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"View project metrics and trends.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Get AI-powered suggestions for growth and optimization.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Track user engagement and feature usage.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Identify potential issues and opportunities.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-slate-400\",\n                                children: [\n                                    \"Navigate to \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                        children: \"AI Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 62\n                                    }, this),\n                                    \" from the sidebar. Select a project to see detailed analytics and recommendations.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    active === \"idea-validator\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"AI Idea Validator\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"The AI Idea Validator helps you assess new SaaS ideas before you build. It provides:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Market Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Size, competition, and demand.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Technical Feasibility\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Complexity and resource needs.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Business Viability\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Revenue projections and validation.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Strategic Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Next steps and actionable insights.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-slate-400\",\n                                children: [\n                                    \"To use: Go to \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                        children: \"AI Idea Validator\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 64\n                                    }, this),\n                                    ' in the sidebar, click \"New Validation\", and fill in your idea details.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    active === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"The Settings page lets you personalize your SaaSifyX experience:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Edit your profile information (name, email, avatar).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Switch between dark, light, or system theme.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Enable or disable email notifications.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Select your preferred language (future feature).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Change your password (stub in demo).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Delete your account (stub in demo).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-slate-400\",\n                                children: \"All changes are saved instantly. Some features require a real backend connection.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    active === \"support\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"Support\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"Need help? The Support page offers:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A contact form to reach our team directly.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"Support email: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"mailto:<EMAIL>\",\n                                                className: \"text-blue-400 underline\",\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 34\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Frequently Asked Questions (FAQ).\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Links to documentation and resources.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-slate-400\",\n                                children: \"For urgent issues, email us directly. For common questions, check the FAQ section.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    active === \"api\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"API Reference\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mb-4\",\n                                children: \"SaaSifyX provides a RESTful API for project and idea management. (API endpoints are for demonstration only.)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"POST /api/ideas\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Submit a new SaaS idea for validation.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"GET /api/ideas\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": List your validated ideas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"GET /api/projects\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": List your SaaS projects.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"PATCH /api/user\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Update user profile and preferences.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"GET /api/insights\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this),\n                                            \": Get analytics and recommendations for your projects.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                        children: \"Note:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" API requires authentication. Use your account token in the \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"Authorization\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 87\n                                    }, this),\n                                    \" header.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    active === \"faq\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-4 text-white\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-300 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I change the theme?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 52\n                                            }, this),\n                                            \"Go to Settings > Theme and select Dark, Light, or System. The UI will update instantly.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I change my password?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 54\n                                            }, this),\n                                            'Go to Settings and click \"Change Password\". (In this demo, this is a stub. In production, you\\'ll receive a password reset email.)'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I delete my account?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 53\n                                            }, this),\n                                            'Go to Settings and click \"Delete Account\". (In this demo, this is a stub. In production, your account and data will be removed.)'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I validate a SaaS idea?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 56\n                                            }, this),\n                                            \"Use the AI Idea Validator from the sidebar, fill in your idea details, and get instant analysis.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I contact support?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 51\n                                            }, this),\n                                            \"Go to the Support page and use the contact form <NAME_EMAIL>.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I navigate the app?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 52\n                                            }, this),\n                                            \"Use the sidebar for all main sections. The top bar provides quick access to your profile and notifications.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"How do I enable or disable notifications?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 67\n                                            }, this),\n                                            \"Go to Settings and use the notifications toggle. (In this demo, this is a UI-only feature.)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Can I change the language?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 52\n                                            }, this),\n                                            \"A language selector is available in Settings. More languages will be supported soon.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"Does SaaSifyX integrate with other tools?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 67\n                                            }, this),\n                                            \"Integrations are planned for future releases. Stay tuned for updates!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                children: \"I'm having trouble logging in or using a feature. What should I do?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 93\n                                            }, this),\n                                            \"First, try refreshing the page or clearing your browser cache. If the issue persists, contact support.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/SaaSifyx/app/docs/page.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_s(DocsPage, \"C2xOut7mqAj3AqnualjJWFT3kc4=\");\n_c = DocsPage;\nvar _c;\n$RefreshReg$(_c, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/docs/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/page.tsx */ \"(app-pages-browser)/./app/docs/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZzYW50aG9zaHBpdGNoYWklMkZEZXNrdG9wJTJGU2FhU2lmeXglMkZhcHAlMkZkb2NzJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBOEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9hcHAvZG9jcy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          },\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        Error(\"react-stack-top-frame\"),\n        createTask(getTaskName(type))\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBpdGNoYWkvRGVza3RvcC9TYWFTaWZ5eC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsanthoshpitchai%2FDesktop%2FSaaSifyx%2Fapp%2Fdocs%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);