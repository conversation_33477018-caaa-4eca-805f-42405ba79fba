# Reactbits Components Documentation

> **Note:** The content of this file has been reorganized into a more structured documentation format.

## New Documentation Location

All reactbits components are now documented in the `/docs/reactbits-components/` directory with the following structure:

- **[index.md](docs/reactbits-components/index.md)**: Main index of all components by category
- **[text-animations-index.md](docs/reactbits-components/text-animations-index.md)**: Index of text animation components
- **[text-animations.md](docs/reactbits-components/text-animations.md)**: Documentation for SplitText and RotatingText components
- **[text-animations-2.md](docs/reactbits-components/text-animations-2.md)**: Documentation for FuzzyText, CountUp, and TextCursor components
- **[text-animations-3.md](docs/reactbits-components/text-animations-3.md)**: Documentation for ScrollFloat component

## Benefits of the New Structure

- Better organization by component category
- Consistent documentation format
- Easier navigation and discovery
- Improved readability
- Integration with the project's design system

## Getting Started

Please refer to the [main index](docs/reactbits-components/index.md) to browse all available components and their documentation.
