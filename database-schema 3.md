# SaaS Ideas - Database Schema & Migrations

## Database Schema

### Overview
The database schema is designed to support the SaaS Ideas application with tables for users, projects, features, user flows, and other related entities.

### Entity Relationship Diagram
```
Users 1──┐
          │
          │ n
          ▼
       Projects 1──┐
                   │
                   │ n
                   ▼
                Features
                   ▲
                   │
                   │ 1
       UserFlows 1─┘
```

## SQL Migrations

### Initial Setup

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  name TEXT,
  avatar_url TEXT,
  -- User management fields
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'pending', 'inactive')),
  role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
  last_login TIMESTAMP WITH TIME ZONE,
  email_verified BOOLEAN DEFAULT FALSE,
  phone TEXT,
  company TEXT,
  job_title TEXT,
  bio TEXT,
  location TEXT,
  website TEXT,
  social_links JSONB DEFAULT '{}',
  preferences JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}'
);

-- Create projects table
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  analysis_data JSONB,
  overall_score NUMERIC(3,1),
  -- AI-generated content storage
  generated_tasks JSONB,
  tasks_explanation TEXT,
  tasks_generated_at TIMESTAMP WITH TIME ZONE
);

-- Create features table
CREATE TABLE features (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  priority TEXT CHECK (priority IN ('low', 'medium', 'high')),
  status TEXT CHECK (status IN ('backlog', 'todo', 'in_progress', 'review', 'done')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  assignee TEXT,
  due_date TIMESTAMP WITH TIME ZONE,
  tags TEXT[]
);

-- Create user_flows table
CREATE TABLE user_flows (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  flow_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  version INTEGER DEFAULT 1
);

-- Create cursor_integrations table
CREATE TABLE cursor_integrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  connected BOOLEAN DEFAULT FALSE,
  last_synced TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cursor_tasks table
CREATE TABLE cursor_tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  feature_id UUID REFERENCES features(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  status TEXT CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  result TEXT
);

-- Create user_activity_logs table
CREATE TABLE user_activity_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  resource_type TEXT,
  resource_id UUID,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_sessions table
CREATE TABLE user_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  session_token TEXT UNIQUE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Create Indexes

```sql
-- Create indexes for better query performance
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_features_project_id ON features(project_id);
CREATE INDEX idx_features_status ON features(status);
CREATE INDEX idx_user_flows_project_id ON user_flows(project_id);
CREATE INDEX idx_cursor_tasks_feature_id ON cursor_tasks(feature_id);
-- AI content indexes
CREATE INDEX idx_projects_tasks_generated_at ON projects(tasks_generated_at);
CREATE INDEX idx_projects_generated_tasks ON projects USING GIN(generated_tasks);
-- User management indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_last_login ON users(last_login);
CREATE INDEX idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX idx_user_activity_logs_action ON user_activity_logs(action);
CREATE INDEX idx_user_activity_logs_created_at ON user_activity_logs(created_at);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
```

### Create Views

```sql
-- Create a view for project statistics
CREATE VIEW project_stats AS
SELECT 
  p.user_id,
  COUNT(p.id) AS total_projects,
  COUNT(CASE WHEN (
    SELECT COUNT(*) FROM features f 
    WHERE f.project_id = p.id AND f.status != 'done'
  ) = 0 AND EXISTS (
    SELECT 1 FROM features f2 WHERE f2.project_id = p.id
  ) THEN 1 END) AS completed_projects,
  COUNT(CASE WHEN EXISTS (
    SELECT 1 FROM features f 
    WHERE f.project_id = p.id AND f.status = 'in_progress'
  ) THEN 1 END) AS in_progress_projects,
  AVG(p.overall_score) AS average_score
FROM projects p
GROUP BY p.user_id;

-- Create a view for feature statistics by project
CREATE VIEW feature_stats AS
SELECT 
  f.project_id,
  COUNT(f.id) AS total,
  COUNT(CASE WHEN f.status = 'backlog' THEN 1 END) AS backlog,
  COUNT(CASE WHEN f.status = 'todo' THEN 1 END) AS todo,
  COUNT(CASE WHEN f.status = 'in_progress' THEN 1 END) AS in_progress,
  COUNT(CASE WHEN f.status = 'review' THEN 1 END) AS review,
  COUNT(CASE WHEN f.status = 'done' THEN 1 END) AS done,
  CASE 
    WHEN COUNT(f.id) > 0 THEN 
      (COUNT(CASE WHEN f.status = 'done' THEN 1 END)::FLOAT / COUNT(f.id)) * 100
    ELSE 0
  END AS completion_percentage
FROM features f
GROUP BY f.project_id;
```

### Create Functions

```sql
-- Function to update project timestamps on any change
CREATE OR REPLACE FUNCTION update_project_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update user timestamps on any change
CREATE OR REPLACE FUNCTION update_user_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update feature timestamps on any change
CREATE OR REPLACE FUNCTION update_feature_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update user_flow timestamps and increment version on change
CREATE OR REPLACE FUNCTION update_user_flow()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  NEW.version = OLD.version + 1;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update cursor_integration timestamps on any change
CREATE OR REPLACE FUNCTION update_cursor_integration_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update cursor_task timestamps on any change
CREATE OR REPLACE FUNCTION update_cursor_task_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### Create Triggers

```sql
-- Create triggers for timestamp updates
CREATE TRIGGER update_project_timestamp
BEFORE UPDATE ON projects
FOR EACH ROW EXECUTE PROCEDURE update_project_timestamp();

CREATE TRIGGER update_user_timestamp
BEFORE UPDATE ON users
FOR EACH ROW EXECUTE PROCEDURE update_user_timestamp();

CREATE TRIGGER update_feature_timestamp
BEFORE UPDATE ON features
FOR EACH ROW EXECUTE PROCEDURE update_feature_timestamp();

CREATE TRIGGER update_user_flow
BEFORE UPDATE ON user_flows
FOR EACH ROW EXECUTE PROCEDURE update_user_flow();

CREATE TRIGGER update_cursor_integration_timestamp
BEFORE UPDATE ON cursor_integrations
FOR EACH ROW EXECUTE PROCEDURE update_cursor_integration_timestamp();

CREATE TRIGGER update_cursor_task_timestamp
BEFORE UPDATE ON cursor_tasks
FOR EACH ROW EXECUTE PROCEDURE update_cursor_task_timestamp();
```

### Row Level Security (RLS) Policies

```sql
-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE features ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_flows ENABLE ROW LEVEL SECURITY;
ALTER TABLE cursor_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE cursor_tasks ENABLE ROW LEVEL SECURITY;

-- Create policies for users
CREATE POLICY user_select ON users
  FOR SELECT USING (auth.uid() = id);
  
CREATE POLICY user_update ON users
  FOR UPDATE USING (auth.uid() = id);

-- Create policies for projects
CREATE POLICY project_select ON projects
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY project_insert ON projects
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY project_update ON projects
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY project_delete ON projects
  FOR DELETE USING (auth.uid() = user_id);

-- Create policies for features
CREATE POLICY feature_select ON features
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = features.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY feature_insert ON features
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = features.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY feature_update ON features
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = features.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY feature_delete ON features
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = features.project_id 
      AND projects.user_id = auth.uid()
    )
  );

-- Create policies for user_flows
CREATE POLICY user_flow_select ON user_flows
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = user_flows.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY user_flow_insert ON user_flows
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = user_flows.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY user_flow_update ON user_flows
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = user_flows.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY user_flow_delete ON user_flows
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = user_flows.project_id 
      AND projects.user_id = auth.uid()
    )
  );

-- Create policies for cursor_integrations
CREATE POLICY cursor_integration_select ON cursor_integrations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = cursor_integrations.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY cursor_integration_insert ON cursor_integrations
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = cursor_integrations.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY cursor_integration_update ON cursor_integrations
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = cursor_integrations.project_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY cursor_integration_delete ON cursor_integrations
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = cursor_integrations.project_id 
      AND projects.user_id = auth.uid()
    )
  );

-- Create policies for cursor_tasks
CREATE POLICY cursor_task_select ON cursor_tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM features
      JOIN projects ON features.project_id = projects.id
      WHERE features.id = cursor_tasks.feature_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY cursor_task_insert ON cursor_tasks
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM features
      JOIN projects ON features.project_id = projects.id
      WHERE features.id = cursor_tasks.feature_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY cursor_task_update ON cursor_tasks
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM features
      JOIN projects ON features.project_id = projects.id
      WHERE features.id = cursor_tasks.feature_id 
      AND projects.user_id = auth.uid()
    )
  );
  
CREATE POLICY cursor_task_delete ON cursor_tasks
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM features
      JOIN projects ON features.project_id = projects.id
      WHERE features.id = cursor_tasks.feature_id 
      AND projects.user_id = auth.uid()
    )
  );
```

## Supabase Setup Instructions

1. Create a new Supabase project
2. Navigate to the SQL Editor
3. Execute the migrations in order:
   - Initial Setup
   - Create Indexes
   - Create Views
   - Create Functions
   - Create Triggers
   - Row Level Security Policies
4. Set up authentication providers in Supabase Auth settings
5. Configure storage buckets if needed for file uploads
6. Set up Edge Functions for AI analysis if required

## Database Access in Next.js

```typescript
// src/lib/supabase/client.ts

import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Server-side client with service role for admin operations
export const getServiceSupabase = () => {
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string;
  return createClient<Database>(supabaseUrl, supabaseServiceKey);
};
``` 