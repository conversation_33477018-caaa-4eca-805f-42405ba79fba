// Test script to verify database setup
const { createClient } = require('@supabase/supabase-js')

// You'll need to replace these with your actual Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'your-supabase-url'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-supabase-anon-key'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testDatabase() {
  console.log('🔍 Testing database setup...\n')

  try {
    // Test 1: Check if projects table exists
    console.log('1. Testing projects table...')
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1)
    
    if (projectsError) {
      console.log('❌ Projects table error:', projectsError.message)
    } else {
      console.log('✅ Projects table accessible')
    }

    // Test 2: Check if tasks table exists
    console.log('\n2. Testing tasks table...')
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .limit(1)
    
    if (tasksError) {
      console.log('❌ Tasks table error:', tasksError.message)
    } else {
      console.log('✅ Tasks table accessible')
    }

    // Test 3: Check table structure
    console.log('\n3. Checking table structures...')
    
    // Check projects table columns
    const { data: projectColumns } = await supabase
      .rpc('get_table_columns', { table_name: 'projects' })
      .single()
    
    console.log('Projects table columns exist:', projectColumns ? '✅' : '❌')

    // Test 4: Test RLS policies (this will fail if not authenticated, which is expected)
    console.log('\n4. Testing Row Level Security...')
    const { error: rlsError } = await supabase
      .from('projects')
      .insert({
        user_id: '00000000-0000-0000-0000-000000000000',
        project_name: 'Test Project',
        project_description: 'Test Description',
        market_feasibility: {},
        suggested_improvements: {},
        core_features: {},
        technical_requirements: {}
      })
    
    if (rlsError && rlsError.message.includes('RLS')) {
      console.log('✅ RLS is working (insert blocked as expected)')
    } else if (rlsError) {
      console.log('⚠️  RLS test result:', rlsError.message)
    } else {
      console.log('⚠️  RLS might not be configured properly')
    }

    console.log('\n🎉 Database test completed!')
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
  }
}

// Run the test
testDatabase()
